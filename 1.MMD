sequenceDiagram
    autonumber

    participant admin as 运营后台
    participant server as pc-attach
    participant logic as pc-attach-logic
    participant crowd as 人群包服务
    participant push as 推送服务
    participant client as PC客户端

    Note over admin, client: 配置创建阶段
    admin->>+server: 创建推送配置
    server->>server: 保存配置信息
    server-->>-admin: 配置创建成功

    Note over admin, client: 全部设备推送分支
    alt 配置为全部设备推送
        server->>+push: 发送全服推送
        push->>client: 全服推送通知
        client->>+logic: 拉取全量有效配置
        logic-->>-client: 返回配置列表
        client->>client: 根据配置控制弹窗展示时机
    else 配置为指定人群包推送
        Note over admin, client: 人群包推送分支
        server->>+crowd: 查询人群包用户列表
        crowd-->>-server: 返回UID列表
        server->>server: 分批处理UID列表
        loop 分批控速推送
            server->>+push: 发送指定用户推送
            push->>client: 推送通知(指定用户)
            client->>+logic: 拉取全量有效配置
            logic-->>-client: 返回配置列表
            client->>client: 根据配置控制弹窗展示时机
        end
    end

    Note over client,logic: 客户端拉取配置时机
