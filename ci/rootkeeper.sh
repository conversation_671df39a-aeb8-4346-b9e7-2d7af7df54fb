#!/bin/bash

echo "current branch: $1"

diff_output=$(git diff --name-status $(git merge-base origin/$1 origin/master) | grep '\.proto$' | grep logicsvr-go)

# diff_output 为空
if [[ "${diff_output}" ]]; then
  echo "禁止修改 logicsvr-go 目录下的 proto 文件"
  exit 1
fi

TARGET_DIR="."
PROTO_COUNT=$(find "$TARGET_DIR" -maxdepth 1 -type f -name "*.proto" | wc -l)
if [ "$PROTO_COUNT" -gt 0 ]; then
  echo "More than one proto file found in the root directory."
  exit 1
fi

TARGET_DIR="tt"
PROTO_COUNT=$(find "$TARGET_DIR" -maxdepth 1 -type f -name "*.proto" | wc -l)
if [ "$PROTO_COUNT" -gt 0 ]; then
  echo "More than one proto file found in the tt root directory."
  exit 1
fi

TARGET_DIR="tt/quicksilver"
PROTO_COUNT=$(find "$TARGET_DIR" -maxdepth 1 -type f -name "*.proto" | wc -l)
if [ "$PROTO_COUNT" -gt 0 ]; then
  echo "More than one proto file found in the tt/quicksilver root directory."
  exit 1
fi

exit 0