syntax = "proto3";

package muse_allocate;

option go_package = "golang.52tt.com/protocol/services/muse-allocate";

import "tt/quicksilver/extension/options/options.proto";

service MuseAllocate {
  option (service.options.service_ext) = {
    service_name: "muse-allocate"
  };

  //  mt相关业务房间下发
  rpc GetMuseAllocateInfo(GetMuseAllocateInfoReq) returns (GetMuseAllocateInfoResp) {}

  rpc GetMuseAllocateNonEnterChannelInfo(GetMuseAllocateNonEnterChannelInfoReq) returns (GetMuseAllocateNonEnterChannelInfoResp) {}

  rpc GetHitNewRecallUser(GetHitNewRecallUserReq) returns (GetHitNewRecallUserResp) {}

  rpc GetAndUpdatePollingTimes(GetAndUpdatePollingTimesReq)returns(GetAndUpdatePollingTimesResp){}

  rpc GetDeliveryAvatars(GetDeliveryAvatarsReq) returns (GetDeliveryAvatarsResp) {}

  rpc SetDeliveryAvatars(SetDeliveryAvatarsReq) returns (SetDeliveryAvatarsResp) {}

  rpc RecordUserDeviceStatus(RecordUserDeviceStatusReq) returns (RecordUserDeviceStatusResp) {}

  rpc GetEveryDayFlashChatRemain(GetEveryDayFlashChatRemainRequest) returns (GetEveryDayFlashChatRemainResponse) {}
  rpc IncreaseEveryDayFlashChatUsedTimes(IncreaseEveryDayFlashChatUsedTimesRequest)returns(IncreaseEveryDayFlashChatUsedTimesResponse){}
  rpc DecreaseEveryDayFlashChatRemain(DecreaseEveryDayFlashChatRemainRequest)returns(DecreaseEveryDayFlashChatRemainResponse){}

  // 即时闪聊匹配条件
  rpc GetFlashChatMatchingCondition (GetFlashChatMatchingConditionReq) returns (GetFlashChatMatchingConditionResp){}
  // 保存用户匹配筛选条件
  rpc SaveUserMatchingCondition (SaveUserMatchingConditionReq) returns (SaveUserMatchingConditionResp) {}
  // 即时闪聊对象年龄性别位置信息
  rpc GetFlashChatObjectInfo (GetFlashChatObjectInfoReq) returns (GetFlashChatObjectInfoResp) {}
  // 保存用户GPS定位信息
  rpc SaveUserLocationInfo (SaveUserLocationInfoReq) returns (SaveUserLocationInfoResp) {}
  // 获取用户GPS定位信息
  rpc GetUserLocationInfo (GetUserLocationInfoReq) returns (GetUserLocationInfoResp) {}
  // 批量获取用户GPS定位信息
  rpc BatchGetUserLocationInfo (BatchGetUserLocationInfoReq) returns (BatchGetUserLocationInfoResp) {}
  // 删除用户GPS定位信息
  rpc DeleteUserLocationInfo (DeleteUserLocationInfoReq) returns (DeleteUserLocationInfoResp) {}

  rpc TestDoUserFlashChatMatchEvent (TestDoUserFlashChatMatchEventReq) returns (TestDoUserFlashChatMatchEventResp) {}

  rpc HasUserChatSilentStatus(HasUserChatSilentStatusReq) returns (HasUserChatSilentStatusResp) {}
  rpc UpdateUserChatSilentStatus(UpdateUserChatSilentStatusReq) returns (UpdateUserChatSilentStatusResp) {}

}

message GetMuseAllocateInfoReq {
  uint32 uid = 1;
  uint32 scene = 2; // 场景
  int32 ui_style = 3;  //0--旧的 1--新的
}

message GetMuseAllocateInfoResp {
  oneof info{
    MuseAllocateChannel channel = 1;
    MuseAllocateChannelV2 channel_v2 = 3;
    MuseAllocateNonEnterChannelInfo new_ui = 4;
  };

  uint32 interval = 2; // 间隔（s）
}

message MuseAllocateChannel {
  string title = 1; // 抬头信息
  MuseAllocateChannelInfo channel_info = 2; // 房间信息
  repeated MuseAllocateUser user_list = 3; // 麦上用户
  MuseAllocateUser reunion_user = 4; // 重逢用户
  string background_img_url = 5;
  string mate_id = 6;
}

message MuseAllocateChannelInfo {
  uint32 id = 1;
  string name = 2;
  uint32 num = 3;
  uint32 tab_id = 4;
}

message MuseAllocateUser {
  uint32 uid = 1;
  string username = 2;
  string nickname = 3;
  int32 sex = 4;
}

// 6.38.0 房间下发优化
message MuseAllocateChannelV2 {
  string title = 1; // 抬头信息
  MuseAllocateChannelInfoV2 channel_info = 2; // 房间信息
  MuseAllocateUserV2 reunion_user = 3; // 展示用户信息
  string background_img_url = 5;
  string background_text_color = 6;
  string mate_id = 7;
}

// 房间信息
message MuseAllocateChannelInfoV2 {
  uint32 channel_id = 1;
  string channel_name = 2;
  uint32 tab_id = 3;
  uint32 user_num = 4; // 在房人数
}

message MuseAllocateUserV2 {
  MuseAllocateUser user_info = 1;
  string user_attr_cert_icon_url = 2; // 用户属性标识
  string user_attr_cert_text=3; // 用户属性文案
}

message GetMuseAllocateNonEnterChannelInfoReq {
  uint32 uid = 1;
  uint32 ui_style = 2;
  int64 unread = 3;
}

message PersonalCertification {
  string id = 1;
  string icon = 2;
  string text = 3;
  repeated string color = 4;
  string text_shadow_color = 5; /* 文字阴影颜色 */
}

message MuseAllocateNonEnterChannelInfo {
  string background_img_url = 1;
  string background_color = 2;
  string tab_name = 3;
  string tab_color = 4;
  MuseAllocateChannelInfoV2 channel_info = 5;
  MuseAllocateUserV2 user_info = 6;
  PersonalCertification cert = 7;
  string channel_message = 8;  //弹窗中间特征文案
  string button_text = 9; // 按钮文案
  string mate_id = 10;
  GloryInfo glory_info=11;//获取称号
}

message GetMuseAllocateNonEnterChannelInfoResp {
  oneof ui {
    MuseAllocateChannelV2 old_ui = 1;
    MuseAllocateNonEnterChannelInfo new_ui = 2;
  };
}

message GloryInfo{
  string glory_name = 5; // 称号名称
  string glory_img = 6; // 头标
  string glory_bg_img = 7; // 背景颜色
  uint32 glory_rank = 8; // 排行
  GloryLevel glory_level = 9;
  string glory_loc_code = 10;//位置code
  string glory_singer_id = 11;//歌手id3
  enum GloryLevel{
    City = 0;//市级称号
    Province = 1;//省级称号
    Country = 2;//国服称号
  }
}

message GetHitNewRecallUserReq{
  uint32 uid = 1;
}

message GetHitNewRecallUserResp{
  map<uint32,bool> uid_map = 1;
}

message GetAndUpdatePollingTimesReq{
    uint32 uid=1;
}

message GetAndUpdatePollingTimesResp{
  int32 times=1;
}

message GetDeliveryAvatarsReq{
  uint32 uid = 1;
}

message GetDeliveryAvatarsResp{
  map<string,bool> avatar_map = 1;
}

message SetDeliveryAvatarsReq{
  uint32 uid = 1;
  repeated string avatar_list = 2;
  uint32 ttl = 3;
  bool is_reset = 4; // 是否重置再写入
}

message SetDeliveryAvatarsResp{
}

message RecordUserDeviceStatusReq{
  uint32 uid = 1;
  string device_id = 2;
}

message RecordUserDeviceStatusResp{
  string device_status = 1;
}


message GetEveryDayFlashChatRemainRequest{
    uint32 uid=1;
}

message GetEveryDayFlashChatRemainResponse{
    int32 remain=1;
    string remain_text=2;
}

message IncreaseEveryDayFlashChatUsedTimesRequest{
  uint32 uid=1;
}

message IncreaseEveryDayFlashChatUsedTimesResponse{

}

message DecreaseEveryDayFlashChatRemainRequest{
  uint32 uid=1;
}

message DecreaseEveryDayFlashChatRemainResponse{
  int32 remain=1;
  string remain_text=2;
}

message GetFlashChatMatchingConditionReq {
  uint32 uid = 1;
}

message GetFlashChatMatchingConditionResp {
  repeated FlashChatMatchingCondition conditions = 1;
  bool   has_open_silent_status=2;   //开启静默状态
}

message FlashChatMatchingCondition {
  string name = 1; // 条件名称
  repeated OptionList list  = 2;
}

message OptionList {
  string text = 1;
  bool select = 2;
  bool is_multiple_select = 3;
}

message SaveUserMatchingConditionReq {
  uint32 uid = 1;
  repeated FlashChatMatchingCondition conditions = 2;
}

message SaveUserMatchingConditionResp {
}

message MatchUserInfo {
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  uint32 gender = 4;
  uint32 follow_status=5; //see FOLLOW_STATUS_TYPE
}

message GetFlashChatObjectInfoReq {
  uint32 target_uid = 1;
  uint32 my_uid = 2;
  bool is_open_location = 3;
}

message GetFlashChatObjectInfoResp {
  string age = 1;
  string position = 2;
  uint32 sex = 3;
}

message SaveUserLocationInfoReq {
  uint32 uid = 1;
  LocationInfo location = 2;
}

message LocationInfo {
  string client_ip_str = 2;
  string city = 3;
  string province   = 4;
  double longitude = 5;
  double latitude = 6;
  string country = 7;
}

message SaveUserLocationInfoResp {
}

message GetUserLocationInfoReq {
  uint32 uid = 1;
}

message GetUserLocationInfoResp {
  LocationInfo location = 1;
}

message BatchGetUserLocationInfoReq {
  repeated uint32 uids = 1;
}

message BatchGetUserLocationInfoResp {
  map<uint32, LocationInfo> location = 1;
}

message DeleteUserLocationInfoReq {
  uint32 uid = 1;
}

message DeleteUserLocationInfoResp {
}

// 闪聊实时匹配成功后推荐下发事件
message UserFlashChatMatchEvent {
  UserFlashChatMatchInfo user_a = 1; // 匹配的用户A
  UserFlashChatMatchInfo user_b = 2; // 匹配的用户B
}

message UserFlashChatMatchInfo {
  uint32 uid = 1; // 主动触发的用户ID
  repeated FlashChatMatchUser user_list = 2; // 匹配的用户列表
  string trace_id = 3;  // trace_id 透传到客户端，客户端埋点需上报这个字段
  uint32 client_version = 4;
}
// 推荐原因
enum ReasonType {
  RT_None = 0; // 无
  RT_SameCity = 1; // 同城
  RT_SameProvince = 2; // 同省
  RT_SameGameCard = 3; // 相同游戏卡
  RT_SameAgeGroup = 4; // 相同年龄段
  RT_SameFlashPoint = 5; // 相同闪光点
  RT_SameInterestTag = 6; // 相同兴趣标签
}

message RcmdReason {
  ReasonType type = 1; // 推荐原因类型
  string content = 2; // 推荐原因内容
}
message FlashChatMatchUser {
  uint32 uid = 1; // 下发的用户ID
  uint32 match_type = 2; // 匹配类型：0-异步匹配，1-实时匹配
  int64 match_time = 3; // 匹配时间戳，单位秒
  repeated RcmdReason rcmd_reasons = 4; // 推荐原因列表
}

message TestDoUserFlashChatMatchEventReq {
  UserFlashChatMatchEvent event = 1;
}

message TestDoUserFlashChatMatchEventResp {
}


message HasUserChatSilentStatusReq{
    uint32 uid=1;
}

message HasUserChatSilentStatusResp{
  bool has_open_silent_status=1;   //开启静默状态
}

message UpdateUserChatSilentStatusReq{
  bool has_open_silent_status=1;   //开启静默状态
  uint32 uid=2;
}

message UpdateUserChatSilentStatusResp{

}


message UserSilentStatusEvent {
  uint32 uid = 1;
  bool has_open_silent_status=2;
  int64 duration_time=3;   //seconds
}