syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/backpacksender";
package backpacksender;


service BackpackSender {
    rpc AddBusiness ( AddBusinessReq )  returns ( AddBusinessResp ){
    }

    rpc GetAllBusiness ( GetAllBusinessReq ) returns ( GetAllBusinessResp ) {
    }

    rpc GetBusinessByIds (GetBusinessByIdsReq) returns (GetBusinessByIdsResp){}

    rpc AddBusinessRiskControlConf ( AddBusinessRiskControlConfReq )  returns ( AddBusinessRiskControlConfResp ){
    }

    // 批量修改或增加风控配置
    rpc BatchAddOrModBusinessRiskControlConf (BatchAddOrModBusinessRiskControlConfReq) returns (BatchAddOrModBusinessRiskControlConfResp){}

    rpc ModBusinessRiskControlConf(ModBusinessRiskControlConfReq) returns (ModBusinessRiskControlConfResp){
    }

    rpc GetBusinessRiskControlConf ( GetBusinessRiskControlConfReq ) returns (GetBusinessRiskControlConfResp) {
    }
    rpc GetBusinessRiskControlConfByBgId (GetBusinessRiskControlConfByBgIdReq) returns (GetBusinessRiskControlConfByBgIdResp){
    }

    rpc SendBackpackWithRiskControl ( SendBackpackWithRiskControlReq )  returns ( SendBackpackWithRiskControlResp ){
    }

    rpc SendOneBackpackWithRiskControl ( SendOneBackpackWithRiskControlReq )  returns ( SendOneBackpackWithRiskControlResp ){
    }

    //后台
  rpc DeductUserBackpackWithRiskControl ( DeductUserBackpackWithRiskControlReq )  returns ( DeductUserBackpackWithRiskControlResp ){
    }

    rpc BatchSendBackpackWithRiskControl ( BatchSendBackpackWithRiskControlReq )  returns ( BatchSendBackpackWithRiskControlResp ){
    }

    //重新下发秘钥邮件
    rpc ReSendSecretKeyEmail(ReSendSecretKeyEmailReq) returns (ReSendSecretKeyEmailResp){
    }

    //前置检查业务风控
    rpc PreCheckBussinessRiskControl(PreCheckBussinessRiskControlReq) returns(PreCheckBussinessRiskControlResp){}
}

enum LimitTimeType {
    UNKOWN = 0;
    HOUR_CNT   = 1;
    DAY_CNT    = 2;
    HOUR_VAL   = 3;
    DAY_VAL    = 4;
}

enum OrderStatus {
    INIT = 0;
    SEND = 1;
}

//T豆包裹类型
enum PresentBusinessType
{
  E_PRESENT_BUSINESS_TYPE_DEFAULT = 0; // 默认业务类型
  E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT = 1; // 亲密礼物
}
message BusinessConf {
    string name = 1; //业务名称
    string desc = 2; //业务描述
    string callback_url = 3; //回调接口
    uint32 source_id = 4; //绑定包裹来源
    string opera_user = 5; //操作者用户名
    string begin_time = 6; //业务开始时间 ’2021-01-18 15:09:00‘
    string end_time   = 7; // 业务结束时间 ’2021-01-18 15:09:00‘
    uint32 business_id = 8;   // 业务id
    string secret_key = 9;    // 秘钥
    uint32 warning_precent = 10; // 告警百分比(0-100)
    string oper_email = 11;    //操作者EMAIL
    uint32 business_type = 12; //T豆包裹类型 see BusinessType
    uint32 activity_id = 13; //活动ID , 在黑色运营后台活动配置页
    string operate_user = 14; // 操作人
}

//添加业务
message AddBusinessReq{
    BusinessConf business_conf = 1;
}

message AddBusinessResp{
    BusinessConf business_conf = 1;
}

//获取所有业务
message GetAllBusinessReq {
}

message GetAllBusinessResp {
    repeated BusinessConf business_list = 1;
}

// 根据id列表获取业务配置
message GetBusinessByIdsReq {
  repeated uint32 id_list = 1;
}
message GetBusinessByIdsResp {
  repeated BusinessConf business_list = 1;
}

message BusinessRiskControlConf{
    uint32 business_id = 1; //业务ID
    uint32 bg_id = 2; //包裹ID(bg_id=0表示所有包裹)
    uint64 hour_cnt_limit = 3; //每小时发放包裹数量限制
    uint64 day_cnt_limit = 4; //每天发放数量
    uint32 single_cnt_limit = 5; //单次发放数量限制
    uint32 single_value_limit = 6; //单次发放价值限制
    uint64 hour_tbean_value_limit = 7; //每小时T豆价值限制
    uint64 day_tbean_value_limit = 8; //每天T豆价值限制
    string opera_user = 9; //操作者用户名
    uint32 id = 10;    // db 自增id
    uint64 used_hour_tbean_value = 11;    //每小时T豆已用价值
    uint64 used_day_tbean_value = 12; //每天T豆价值已用价值
    uint64 used_hour_cnt = 13;          // 每小时发放包裹已用数量
    uint64 used_day_cnt = 14;     // 每天发放数量已用数量
    int64 rest_hour_tbean_value = 15;    //每小时T豆价值限制余额
    int64 rest_day_tbean_value = 16; //每天T豆价值限制余额
    int64 rest_hour_cnt = 17;          // 每小时发放包裹数量限制余额
    int64 rest_day_cnt = 18;     // 每天发放数量余额
    string oper_email = 19;    //操作者EMAIL
    uint64 user_hour_cnt_limit = 20; //用户维度-每小时发放包裹数量限制
    uint64 user_day_cnt_limit = 21; //用户维度-每天发放数量
    uint64 user_hour_tbean_value_limit = 22; //用户维度-每小时T豆价值限制
    uint64 user_day_tbean_value_limit = 23; //用户维度-每天T豆价值限制
    bool   is_cur_all_bg = 24; //是否当前配置的所有包裹 (bg_id=0时才生效)
}

//添加业务风控配置
message AddBusinessRiskControlConfReq{
    BusinessRiskControlConf business_risk_conf = 1;
}

message AddBusinessRiskControlConfResp {
    BusinessRiskControlConf business_risk_conf = 1;
}

// 批量修改或增加风控配置
message BatchAddOrModBusinessRiskControlConfReq {
  repeated BusinessRiskControlConf add_risk_list = 1;
  repeated BusinessRiskControlConf mod_risk_list = 2;
}
message BatchAddOrModBusinessRiskControlConfResp {

}

message ModBusinessRiskControlConfReq{
    BusinessRiskControlConf business_risk_conf = 1;
}
message ModBusinessRiskControlConfResp{
}

message GetBusinessRiskControlConfReq {
    uint32 business_id = 1;
    repeated uint32 business_id_list = 2;
}

message BusinessRiskControlConfList {
    repeated BusinessRiskControlConf business_risk_conf_list = 1;
}

message GetBusinessRiskControlConfResp {
    repeated BusinessRiskControlConf business_risk_conf_list = 1;
    map<uint32,BusinessRiskControlConfList> business_2_risk_conf_list = 2;
    uint32 business_2_risk = 3;
}

//根据包裹ID获取业务风控配置
message GetBusinessRiskControlConfByBgIdReq {
    uint32 bg_id = 1;
}

message GetBusinessRiskControlConfByBgIdResp {
    repeated BusinessRiskControlConf business_risk_conf_list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SendBackpackOrderInfo {
   uint32 business_id = 1; //业务ID
   uint32 backpack_id = 2; //包裹ID
   uint32 receive_uid = 3; //收礼人UID
   uint32 backpack_cnt = 4; //发放数量
   uint32 sourceId     = 5; //来源ID
   int64 outside_time  = 6; //发背包业务时间
   int64 server_time   = 7;//风控服务时间
   string order_id     = 8; //订单号，订单格式 业务ID_xxx
   bytes ciphertext   = 9; //密文用于权限验证，订单号密文
   uint32 expire_duration = 10;
   string source_app_id = 11; //来源明细
   string traffic_mark  = 12; //流量标识
}

message SendBackpackWithRiskControlReq {
   uint32 business_id = 1; //业务ID
   uint32 backpack_id = 2; //包裹ID
   uint32 receive_uid = 3; //收礼人UID
   uint32 backpack_cnt = 4; //发放数量
   int64 server_time  = 5; //业务时间
   string order_id     = 6; //订单号，订单格式 业务ID_xxx
   bytes ciphertext   = 7; //密文用于权限验证，订单号密文
   uint32 expire_duration = 8; //按照自然天过期该物品；example:expire_duration=1
   string source_app_id = 9; //来源明细//
   string deal_token = 10; //dealtoken
}

message SendBackpackWithRiskControlResp{
}

message SendOneBackpackWithRiskControlReq {
    uint32 business_id = 1; //业务ID
    uint32 backpack_id = 2; //包裹ID
    uint32 receive_uid = 3; //收礼人UID
    int64 server_time  = 4; //业务时间
    string order_id     = 5; //订单号，订单格式 业务ID_xxx
    bytes ciphertext   = 6; //密文用于权限验证，订单号密文
    uint32 expire_duration = 7; //按照自然天过期该物品；example:expire_duration=1
    string source_app_id = 8; //来源明细//
    string deal_token = 9; //dealtoken
}

message SendOneBackpackWithRiskControlResp{
}


//扣除物品相关

message DeductUserBackpackWithRiskControlReq {
    uint32 business_id = 1; //业务ID
    int64 server_time  = 2; //业务时间
    string order_id     = 3; //订单号(外部订单号，并非风控系统本身的订单号)
    bytes ciphertext   = 4; //密文用于权限验证，订单号密文
  string oper = 5; // 操作者
  uint32 deduct_type = 6; // DeductType
  repeated DeductDetail deduct_list = 7; //要扣除物品的信息
}

message DeductItem{
  uint32 item_type = 1;
  uint32 source_id = 2;
  uint32 count = 3;
}

message DeductDetail {
  uint32 uid = 1;
  repeated DeductItem item_list = 2;
  uint32 count = 3;
  uint32 source_type = 4;
  bool is_all_source = 5;
}

enum DeductType
{
    UNKNOW_DEDUCT_TYPE=0;
    OPRATE_DEDUCT=1;      // 运营扣除（手动）
    BUSINESS_DEDUCT=2;      // 业务扣除
}

enum DeductFailType{
  DEDUCTFAILTYPE_UNVALID = 0;               // 未知类型
  DEDUCTFAILTYPE_UID_NOT_EXIST = 1;               // 错误类型：uid不存在
  DEDUCTFAILTYPE_ITEM_NOT_ENOUGH = 2;                // 错误类型：物品数量不足
  DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL = 3;                // 错误类型：扣除物品失败
}

message DeductResult {
  uint32 uid = 1;
  repeated DeductItem success_item_list = 2;
  repeated DeductItem fail_item_list = 3;
  uint32 fail_type = 4; //失败类型DeductFailType
}

message DeductUserBackpackWithRiskControlResp{
  repeated DeductResult deduct_list = 1; //实际回滚物品的信息
}

message SendPackDetail{
    uint32 uid = 1; //收包裹UID
    uint32 bg_id = 2;//发放的包裹ID
    uint32 bg_cnt = 3; //发放包裹数量
    uint32 expire_duration = 4; //包裹里面道具过期时间，如果不填就读包裹配置
}

message BatchSendBackpackWithRiskControlReq{
    uint32 business_id = 1; //业务ID
    string operation_name = 2; //操作人
    int64 server_time  = 3; //审批订单时间
    string order_id     = 4; //审批订单号
    bytes ciphertext   = 5; //密文用于权限验证，订单号密文

    repeated SendPackDetail send_pack_list = 6;
}

message BatchSendBackpackWithRiskControlResp{
}

message PackageItemCfg {
    uint32 item_type = 1;
    uint32 item_id   = 2;
    uint32 item_cnt  = 3;
}

message BackpackReceiveEvent {
    string order_id = 1;
    uint32 uid = 2;
    uint32 bg_id = 3;
    uint32 cnt = 4;
    uint32 business_id = 5;
    int64 create_time = 6;
    repeated  PackageItemCfg item_list=7;
}

message ReSendSecretKeyEmailReq {
    uint32 business_id = 1;
    bool   is_recreate   = 2;  //是否重新生成
}
message ReSendSecretKeyEmailResp {
}

// 前置风控系统检查业务风控
message PreCheckBussinessRiskControlReq {
  uint32 business_id = 1;
  map<uint32, uint32> bgid_cnts = 2; // 包裹ID对应的数量
}

message PreCheckBussinessRiskControlResp {
}