syntax = "proto3";

package present_set;

import "tt/quicksilver/revenue-api-go/revenue-api-go.proto";
option go_package = "golang.52tt.com/protocol/services/present-set";


// 礼物套组
service PresentSet {

  // 创建礼物套组
  rpc CreatePresentSet(CreatePresentSetReq) returns (CreatePresentSetResp) {}
  // 获取礼物套组信息
  rpc GetPresentSetInfoNoCache(GetPresentSetInfoReq) returns (GetPresentSetInfoResp) {}
  // 获取礼物套组列表
  rpc GetPresentSetListNoCache(GetPresentSetListNoCacheReq) returns (GetPresentSetListNoCacheResp) {}
  // 获取礼物套组列表
  rpc GetPresentSetSimpleListNoCache(GetPresentSetSimpleListNoCacheReq) returns (GetPresentSetSimpleListNoCacheResp) {}
  // 设置套组内礼物
  rpc SetPresentsToSet(SetPresentsToSetReq) returns (SetPresentsToSetResp) {}
  // 删除套组
  rpc DeletePresentSet(DeletePresentSetReq) returns (DeletePresentSetResp) {}
  // 获取套组礼物信息列表
  rpc GetAwardsToSetList(GetAwardsToSetListReq) returns (GetAwardsToSetListResp) {}
  // 设置套组内奖励
  rpc SetAwardsToSet(SetAwardsToSetReq) returns (SetAwardsToSetResp) {}
  // 删除套组内奖励
  rpc DeleteAwardsToSet(DeleteAwardsToSetReq) returns (DeleteAwardsToSetResp) {}
  // 检查礼物是否处于活跃套组中（管理后台接口）
  rpc CheckPresentInActiveSet(CheckPresentInActiveSetReq) returns (CheckPresentInActiveSetResp) {}
  // 获取套组礼物配置
  rpc GetSetPresentConfigList(GetSetPresentConfigListReq) returns (GetSetPresentConfigListResp) {}

  // 获取礼物套组信息
  rpc GetPresentSetInfo(GetPresentSetInfoReq) returns (GetPresentSetInfoResp) {}
  // 获取用户礼物套组信息
  rpc GetUserSetsInfo(GetUserSetsInfoReq) returns (GetUserSetsInfoResp) {}
  // 获取礼物套组列表
  rpc GetPresentSetList(GetPresentSetListReq) returns (GetPresentSetListResp) {}
  // 用户送出套组礼物
  rpc UserSentSetPresent(UserSentSetPresentReq) returns (UserSentSetPresentResp) {}
  // 检查礼物是否处于活跃套组中（查缓存接口）
  rpc CheckPresentInActiveSetWithCache(CheckPresentInActiveSetWithCacheReq) returns (CheckPresentInActiveSetWithCacheResp) {}
  // 检查礼物是否可以赠送
  rpc CheckPresentValid(CheckPresentValidReq) returns (CheckPresentValidResp) {}

  // 创建帝王套
  rpc CreateEmperorSet(CreateEmperorSetReq) returns (CreateEmperorSetResp) {}
  // 获取帝王套列表
  rpc GetEmperorSetListNoCache(GetEmperorSetListNoCacheReq) returns (GetEmperorSetListNoCacheResp) {}
  // 获取帝王套信息
  rpc GetEmperorSetInfoNoCache(GetEmperorSetInfoReq) returns (GetEmperorSetInfoResp) {}
  // 删除帝王套
  rpc DeleteEmperorSet(DeleteEmperorSetReq) returns (DeleteEmperorSetResp) {}

  // 获取帝王套列表
  rpc GetEmperorSetList(GetEmperorSetListReq) returns (GetEmperorSetListResp) {}
  // 获取帝王套信息
  rpc GetEmperorSetInfo(GetEmperorSetInfoReq) returns (GetEmperorSetInfoResp) {}
  // 校验礼物是否在生效的帝王套中
  rpc CheckPresentInActiveEmperorSetWithCache(CheckPresentInActiveEmperorSetWithCacheReq) returns (CheckPresentInActiveEmperorSetWithCacheResp) {}
  // 判断用户是否可以根据礼物套组送帝王套
  rpc ValidUserCanSendSetByEmperorSetId(ValidUserCanSendSetByEmperorSetIdReq) returns (ValidUserCanSendSetByEmperorSetIdResp) {}

  // 处理帝王套送出
  rpc UserSentEmperorSetPresent(UserSentEmperorSetPresentReq) returns (UserSentEmperorSetPresentResp) {}
  // 获取用户帝王套历史
  rpc GetUserEmperorSetHistory(GetUserEmperorSetHistoryReq) returns (GetUserEmperorSetHistoryResp) {}
  // 获取用户帝王套汇总
  rpc GetEmperorSetSummary(GetEmperorSetSummaryReq) returns (GetEmperorSetSummaryResp) {}

  // 测试接口 重置用户信息
  rpc TestResetUserInfo(TestResetUserInfoReq) returns (TestResetUserInfoResp) {}
  // 测试接口 获取用户套组当前抽奖信息
  rpc TestGetUserSetLotteryInfo(TestGetUserSetLotteryInfoReq) returns (TestGetUserSetLotteryInfoResp) {}
}

// 礼物套组配置
message PresentSetConfig {
  uint32 set_id = 1; // 套组id
  string set_name = 2; // 套组名称
  PresentSetActiveStatus active_status = 3; // 活动状态
  uint32 create_time = 4; // 创建时间
  double rank = 5; // 礼物架排序
  string award_text = 11; // 奖励说明文案
  // group
  PresentSetPeriod period = 6; // 时效
  uint32 start_time = 7; // 开始时间
  uint32 end_time = 8; // 结束时间
  // group
  // bool enable_king_set = 9; // 是否配置帝王套（废弃）
  // uint32 king_set_id = 10; // 帝王套id（废弃）
  // group
  bool enable_preview = 12; // 是否配置预览
  PresentSetPreviewType preview_type = 23; // 预览类型
  string preview_res_url = 13; // 预览资源链接
  string preview_res_md5 = 25; // 预览资源md5
  // group
  bool enable_activity = 14; // 是否配置活动
  string activity_entrance_res_url = 15; // 活动入口资源
  ActivityLinkType activity_link_type = 24; // 活动链接类型
  string activity_jump_url = 16; // 活动链接
  // end
  repeated SetPresentItem present_list = 17; // 礼物列表
  repeated AwardItem award_item_list = 18; // 套组奖励列表
  uint32 update_time = 19; // 更新时间
  uint32 change_time = 20; // 配置变更时间（包含套组配置、礼物配置、奖励配置变更）
  uint32 breaking_news_id = 21; // 全服ID
  SetPresentItem origin_present = 22; // 初始礼物信息
  // group
  bool enable_emperor_set = 26; // 是否配置帝王套
  uint32 emperor_set_id = 27; // 帝王套id
  bool is_del = 28; // 是否已删除
}

// 礼物套组中的礼物配置
message SetPresentItem {
  uint32 set_id = 1; // 套组id
  uint32 present_id = 2; // 礼物id
  string present_name = 3; // 礼物名称
  string present_icon = 4; // 礼物图标
  PresentLevel present_level = 5; // 礼物等级
  bool is_origin = 6; // 是否初始礼物
  double probability = 7; // 概率（%）
  double rank = 8; // 套组内排序
  uint32 threshold = 9; // 门槛次数
  int32 guaranteed = 10; // 保底次数
  string background_url = 11; // 背景图
  uint32 present_price = 12; // 礼物价值
}

// 礼物套组奖励配置
message SetAwardItem {
  uint32 set_id = 1; // 套组id
  string set_name = 2; // 套组名称
  repeated AwardItem award_item_list = 3; // 套组奖励列表
}

// 套组奖励
message AwardItem {
  revenue_api_go.RevenueAwardType award_type = 1; // 奖励类型
  string award_id = 2; // 物品ID
  uint32 award_count = 3; // 奖励天数/奖励个数
  uint32 active_count = 4; // 生效个数
  uint32 rank = 5; // 奖励排序
  string award_pic = 6; // 奖励图标（设置时不填）
  uint32 award_price = 7; // 奖励价值（设置时不填）
  string award_count_info = 8; // 外显奖励形式（设置时不填）
  string award_name = 9; // 奖励名称（设置时不填）
}

// 帝王套配置
message EmperorSetConfig {
  uint32 set_id = 1; // 帝王套ID
  string set_name = 2; // 帝王套名称
  PresentSetActiveStatus active_status = 3; // 活动状态
  uint32 create_time = 4; // 创建时间
  uint32 update_time = 5; // 更新时间
  string icon_url = 6; // 帝王套图标
  bool is_fuse_user_info = 7; // 是否融合用户信息
  EmperorSetEffect effect = 8; // 前置特效
  // group
  bool show_present_shelf = 10; // 是否展示礼物架
  double rank = 11; // 礼物架排序
  bool show_effect_end = 12; // 是否展示礼物下架时间
  // group
  uint32 start_time = 15; // 开始时间
  uint32 end_time = 16; // 结束时间
  // group
  string banner_res_url = 20; // 横幅资源链接
  EmperorSetBannerJumpType banner_jump_type = 21; // 横幅跳转类型
  string banner_jump_url = 22; // 横幅跳转链接
  // group
  bool enable_breaking_news = 25; // 是否配置全服播报
  uint32 breaking_news_id = 26; // 全服ID
  int32 breaking_times_limit = 27; // 全服次数限制
  // end
  repeated EmperorSetPresentItem present_list = 30; // 帝王套礼物列表
  uint32 presents_total_price = 31; // 礼物总价值
  bool is_del = 32; // 是否已删除
  string send_emperor_set_text = 33; // 按照帝王套赠送全部的文案
  string send_emperor_set_frame_url = 34; // 按照帝王套赠送全部礼物的边框
  uint32 activity_id = 35; // 活动ID
  string operate_user = 36; // 操作人

  message EmperorSetEffect {
    string android_effect_url = 1; // android前置特效地址
    string android_effect_md5 = 2; // android前置特效MD5
    string ios_effect_url = 3; // ios前置特效地址
    string ios_effect_md5 = 4; // ios前置特效MD5

    bool is_breaking_box = 5; // 是否全服开箱
    string emperor_viewing_source_url = 6; // 帝王套观景台资源地址
    string emperor_viewing_source_md5 = 7; // 帝王套观景台资源MD5
    string emperor_flow_source_url = 8; // 帝王套流光资源地址
    string emperor_flow_source_md5 = 9; // 帝王套流光资源MD5
  }
}

// 帝王套礼物配置
message EmperorSetPresentItem {
  uint32 set_id = 1; // 帝王套ID
  uint32 present_id = 2; // 礼物id
  double rank = 6; // 礼物排序
  string present_name = 3; // 礼物名称
  string present_icon = 4; // 礼物图标
  uint32 present_price = 5; // 礼物价值
  bool show_present_shelf = 7; // 是否展示礼物架 !StPresentItemConfig.Extend.UnshowBatchOption
  uint32 effect_begin = 8; // 上架时间 StPresentItemConfig.EffectBegin
  uint32 effect_end = 9; // 下架时间 StPresentItemConfig.EffectEnd
}

// 礼物等级
enum PresentLevel {
  PRESENT_LEVEL_UNSPECIFIED = 0;
  PRESENT_LEVEL_R = 10;
  PRESENT_LEVEL_SR = 20;
  PRESENT_LEVEL_SSR = 30;
}

// 活动时效
enum PresentSetPeriod {
  PRESENT_SET_PERIOD_UNSPECIFIED = 0;
  PRESENT_SET_PERIOD_LIMITED = 1; // 限时
  PRESENT_SET_PERIOD_LONG = 2; // 长期
}

// 活动状态
enum PresentSetActiveStatus {
  PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED = 0;
  PRESENT_SET_ACTIVE_STATUS_INACTIVE = 1; // 活动未开始
  PRESENT_SET_ACTIVE_STATUS_ACTIVE = 2; // 活动开始
  PRESENT_SET_ACTIVE_STATUS_ENDED = 3; // 活动结束
}

// 预览类型
enum PresentSetPreviewType {
  PRESENT_SET_PREVIEW_TYPE_UNSPECIFIED = 0;
  PRESENT_SET_PREVIEW_TYPE_IMAGE = 1; // 图文
  PRESENT_SET_PREVIEW_TYPE_VIDEO = 2; // 视频
}

// 收集来源类型
enum CollectSourceType {
  COLLECT_SOURCE_TYPE_UNSPECIFIED = 0;
  COLLECT_SOURCE_TYPE_GUARANTEED = 1; // 保底
  COLLECT_SOURCE_TYPE_LOTTERY = 2; // 抽奖
}

// 礼物套组活动链接类型
enum ActivityLinkType {
  ACTIVITY_LINK_TYPE_UNSPECIFIED = 0;
  ACTIVITY_LINK_TYPE_HALF_SCREEN = 1; // 半屏
  ACTIVITY_LINK_TYPE_FULL_SCREEN = 2; // 全屏
}

// 帝王套横幅跳转类型
enum EmperorSetBannerJumpType {
  EmperorSetBannerJumpTypeNone = 0; // 默认值
  EmperorSetBannerJumpTypeCMS = 1; // 跳转CMS
  EmperorSetBannerJumpTypeActivityUrl = 2; // 跳转活动链接
}

message CreatePresentSetReq {
  PresentSetConfig present_set = 1;
  bool is_validate = 3; // 仅校验数据
}
message CreatePresentSetResp {
  uint32 set_id = 1; // 套组ID
}

message GetPresentSetInfoReq {
  uint32 set_id = 1; // 套组ID
}
message GetPresentSetInfoResp {
  PresentSetConfig present_set = 1;
}

message GetPresentSetListNoCacheReq {
  PresentSetActiveStatus active_status = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  uint32 set_id = 7; // 搜索套组ID
  string set_name = 8; // 搜索套组名称
}
message GetPresentSetListNoCacheResp {
  repeated PresentSetConfig present_set_list = 1;
  uint32 total = 2;
}

message GetPresentSetSimpleListNoCacheReq {
  string keyword = 4; // 搜索关键字
}
message GetPresentSetSimpleListNoCacheResp {
  repeated PresentSetConfig present_set_list = 1;
}

message DeletePresentSetReq {
  uint32 set_id = 1; // 套组ID
}
message DeletePresentSetResp {

}

message SetPresentsToSetReq {
  uint32 set_id = 1; // 套组ID
  repeated SetPresentItem present_list = 2; // 礼物列表（覆盖）
  bool is_validate = 3; // 仅校验数据
}
message SetPresentsToSetResp {

}

message GetUserSetsInfoReq {
  uint32 uid = 1; // 用户ID
  uint32 set_id = 2; // 套组ID（空则全部）
}
// 用户套组信息
message UserSetInfo {
  // 用户礼物信息
  message UserPresentInfo {
    uint32 present_id = 1; // 礼物ID
    bool is_collected = 2; // 已收集
    bool is_new = 3; // 新获得（获得但未送出）
  }
  uint32 set_id = 1; // 套组ID
  uint32 uid = 2; // 用户ID
  uint32 last_present_id = 3; // 上次送出的礼物ID
  repeated UserPresentInfo present_list = 4; // 用户礼物信息列表
  uint32 collect_total = 5; // 礼物总数
  uint32 collect_progress = 6; // 礼物收集进度
  uint32 rarest_present_id = 7;  // 最稀有的礼物
}
// 用户播报信息
message UserBroadcast {
  uint32 uid = 1; // 用户ID
  uint32 set_id = 2; // 套组ID
  uint32 present_id = 3; // 礼物ID
}
message GetUserSetsInfoResp {
  repeated UserSetInfo user_set_info_list = 1; // 用户套组信息列表
  repeated UserBroadcast user_broadcast_list = 2; // 用户播报信息列表
  uint32 last_update_time = 3; // 最后更新时间
  string send_all_emperor_set_text = 4; // 按照帝王套赠送全部的文案
}

message SetAwardsToSetReq {
  SetAwardItem award_items = 1; // 套组奖励（覆盖）
  bool is_validate = 2; // 仅校验数据
  bool is_create = 3; // 是否新增
}
message SetAwardsToSetResp {

}

message DeleteAwardsToSetReq {
  uint32 set_id = 1; // 套组ID
}
message DeleteAwardsToSetResp {

}

message GetAwardsToSetListReq {
  string set_id = 1; // 套组ID(模糊查询，不传查全部)
  string set_name = 2; // 套组名称(模糊查询，不传查全部)
  uint32 offset = 3;
  uint32 limit = 4;
}
message GetAwardsToSetListResp {
  repeated SetAwardItem set_award_item_list = 1; // 套组奖励信息列表
  uint32 total = 2;
}

message GetPresentSetListReq {
}
message GetPresentSetListResp {
  repeated PresentSetConfig present_set_list = 1;
}

message UserSentSetPresentReq {
  uint32 uid = 1; // 送礼用户
  uint32 present_id = 2; // 礼物ID
  uint32 present_count = 3; // 礼物数量
  repeated string order_id = 4; // 订单ID
  uint32 send_time = 5; // 送礼时间
}

message UserSentSetPresentResp {

}

message CheckPresentInActiveSetReq {
  uint32 present_id = 1; // 礼物ID
}
message CheckPresentInActiveSetResp {
}

message CheckPresentInActiveSetWithCacheReq {
  uint32 present_id = 1; // 礼物ID
}
message CheckPresentInActiveSetWithCacheResp {
  bool is_active = 1; // 是否激活
}

message TestResetUserInfoReq {
  uint32 uid = 1; // 用户ID
}
message TestResetUserInfoResp {

}

message GetSetPresentConfigListReq {
}
message SimpleStPresentItemConfig {
  uint32 item_id = 1; // 礼物ID
  string name = 2; // 礼物名称
  string icon_url = 3; // 礼物图标
  uint32 price = 4; // 礼物价值
  uint32 rank = 5; // 排名
  uint32 effect_begin = 6; // 上架时间
  uint32 effect_end = 7; // 下架时间
  bool   is_del = 8; // 是否已删除
  uint32 tag = 9; // ga::PresentTagType
  bool is_test = 10; // 该礼物是否是测试 只有白名单用户可以拉取到
}
message GetSetPresentConfigListResp {
  repeated SimpleStPresentItemConfig present_list = 1;
}

message CheckPresentValidReq {
  uint32 present_id = 1; // 礼物ID
  uint32 uid = 2; // uid
}

message CheckPresentValidResp {
}

message LotteryPresentInfo {
  uint32 present_id = 1; // 礼物ID
  string present_name = 2; // 礼物名称
  bool is_origin = 3; // 是否初始礼物
  bool is_collected = 4; // 是否已收集
  uint32 threshold = 5; // 门槛数
  bool is_reach_threshold = 6; // 是否达到门槛
  int32 guaranteed = 7; // 保底数
  bool is_reach_guaranteed = 8; // 是否达到保底（ >= guaranteed ）
  bool is_trigger_guaranteed = 9; // 是否触发保底 ( >= guaranteed + 1 )，至少为达到保底后下一次送礼
  double probability = 10; // 概率
  bool is_lottery = 11; // 是否抽奖
}

message TestGetUserSetLotteryInfoReq {
  uint32 uid = 1; // 用户ID
  uint32 set_id = 2; // 套组ID
}
message TestGetUserSetLotteryInfoResp {
  message PoolInfo {
    uint32 present_id = 1; // 礼物ID
    string present_name = 2; // 礼物名称
    int32 guaranteed = 3; // 保底数
    string probability = 4; // 概率
  }
  uint32 uid = 1; // 用户ID
  uint32 set_id = 2; // 套组ID
  repeated LotteryPresentInfo lottery_present_info_list = 4; // 礼物信息
  uint32 send_present_counter = 5; // 套组累计送礼数
  repeated uint32 collected_present_list = 6; // 已收集礼物列表
  repeated PoolInfo pool_info_list = 7; // 奖池信息
  string lottery_probability = 8; // 中奖总概率
}

message CreateEmperorSetReq {
  EmperorSetConfig emperor_set = 1;
  bool is_validate = 2; // 仅校验数据
}

message CreateEmperorSetResp {
  uint32 set_id = 1; // 帝王套ID
}

message GetEmperorSetListNoCacheReq {
  uint32 offset = 1;
  uint32 limit = 2;
  string set_name = 3;
  uint32 set_id = 4;
  bool include_del = 5; // 是否包含已删除
}
message GetEmperorSetListNoCacheResp {
  repeated EmperorSetConfig emperor_set_list = 1;
  uint32 total = 2;
}

message GetEmperorSetListReq {
  bool include_del = 5; // 是否包含已删除
}
message GetEmperorSetListResp {
  repeated EmperorSetConfig emperor_set_list = 1;
}

message GetEmperorSetInfoReq {
  uint32 set_id = 1; // 帝王套ID
  bool include_del = 5; // 是否包含已删除
}
message GetEmperorSetInfoResp {
  EmperorSetConfig emperor_set = 1;
}

message DeleteEmperorSetReq {
  uint32 set_id = 1; // 帝王套ID
}
message DeleteEmperorSetResp {

}

message CheckPresentInActiveEmperorSetWithCacheReq {
  uint32 present_id = 1; // 礼物ID
}
message CheckPresentInActiveEmperorSetWithCacheResp {
  bool is_active = 1; // 是否激活
}

message ValidUserCanSendSetByEmperorSetIdReq {
  uint32 uid = 1; // 用户uid
  uint32 emperor_set_id = 2; // 帝王套id
  int64 send_time = 3; // 送礼时间
}
message ValidUserCanSendSetByEmperorSetIdResp {
  bool can_send = 1; // 是否可以送
}

message UserSentEmperorSetPresentReq {
  uint32 uid = 1;
  uint32 target_uid = 2;
  uint32 set_id = 3;
  string order_id = 4;   // 帝王套的主订单id
  uint32 send_time = 5;
  string from_ukw_account = 6;  // 送礼神秘人账号，为空则不是神秘人
  string from_ukw_nickname = 7;  // 送礼神秘人昵称
  string to_ukw_account = 8;  // 收礼神秘人账号，为空则不是神秘人
  string to_ukw_nickname = 9;  // 收礼神秘人昵称
  uint32 channel_id = 10;  // 频道id
  uint32 score = 11;  // 送礼积分
  uint32 add_rich = 12;  // 送礼财富
  uint32 add_charm = 13;  // 送礼魅力
  uint32 guild_id = 14;  // 公会id
}

message UserSentEmperorSetPresentResp {

}

message GetUserEmperorSetHistoryReq {
  uint32 uid = 1;
}

message GetUserEmperorSetHistoryResp {
  repeated EmperorSetHistory emperor_set_list = 1;
}

message GetEmperorSetSummaryReq {
  uint32 summary_id = 1;   // id根据summary_type来确定
  uint32 summary_type = 2; // 1:赠送方uid  2:接收方uid 3:房间id 4:公会id
  uint32 sub_id = 3; // 子id，一般不填
}

message GetEmperorSetSummaryResp {
  repeated EmperorSetSummary emperor_sum_list = 1;
}

message EmperorSetHistory
{
  uint32 uid = 1;
  uint32 target_uid = 2;
  uint32 set_id = 3;
  uint32 send_time = 4;
  bool is_ukw = 5;
  uint32 channel_id = 6;  // 频道id
}

message EmperorSetSummary
{
  uint32 set_id = 1;
  uint32 count = 2;
  uint32 single_price = 3; // 单价
  float rank =4 ;
  uint32 begin_time = 5;
  uint32 end_time = 6;
}
