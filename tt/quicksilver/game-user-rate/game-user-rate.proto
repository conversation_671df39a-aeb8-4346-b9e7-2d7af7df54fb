syntax = "proto3";
import "tt/quicksilver/game-user-rate/admin-game-user-rate.proto";

package game_user_rate;

option go_package = "golang.52tt.com/protocol/services/game-user-rate";

service GameUserRate {
  //////////////////////////// 运营后台的接口 /////////////////////////////
  // 运营后台的接口，接口名前加上Admin区分，对应结构体可放到admin_game_user_rate.proto
  // 新增/修改维度配置
  rpc AdminUpsertDimension(AdminUpsertDimensionReq) returns (AdminUpsertDimensionResp) {}
  // 获取维度配置列表
  rpc AdminGetDimensions(AdminGetDimensionsReq) returns (AdminGetDimensionsResp) {}
  // 删除维度配置
  rpc AdminDelDimension(AdminDelDimensionReq) returns (AdminDelDimensionResp) {}

  // 保存最新房间标签配置
  rpc AdminSaveRateLabelList(AdminSaveRateLabelListReq) returns (AdminSaveRateLabelListResp) {}
  // 获取房间标签配置列表
  rpc AdminGetRateLabels(AdminGetRateLabelsReq) returns (AdminGetRateLabelsResp) {}

  // 绑定玩法与维度、标签
  rpc AdminBindGameDimensionLabel(AdminBindGameDimensionLabelReq) returns (AdminBindGameDimensionLabelResp) {}
  // 获取玩法绑定的维度、标签
  rpc AdminGetGameDimensionLabels(AdminGetGameDimensionLabelsReq) returns (AdminGetGameDimensionLabelsResp) {}
  // 删除玩法绑定维度、标签关系
  rpc AdminDelGameDimensionLabel(AdminDelGameDimensionLabelReq) returns (AdminDelGameDimensionLabelResp) {}

  // 添加点评展示与处罚配置
  rpc AddShowRateAndPunishConf(AddShowRateAndPunishConfReq) returns (AddShowRateAndPunishConfResp) {}
  // 更新点评展示与处罚配置
  rpc UpdateShowRateAndPunishConf(UpdateShowRateAndPunishConfReq) returns (UpdateShowRateAndPunishConfResp) {}
  // 获取点评展示与处罚配置
  rpc GetShowRateAndPunishConf(GetShowRateAndPunishConfReq) returns (GetShowRateAndPunishConfResp) {}


  //////////////////////////// H5调用的接口 /////////////////////////////
  rpc GetGameUserRateById(GetGameUserRateByIdReq) returns(GetGameUserRateByIdResp) {}
  // 获取用户评价列表
  rpc GetGameUserRateList(GetGameUserRateListReq) returns(GetGameUserRateListResp) {}
  // 用户提交评价
  rpc SubmitGameUserRate(SubmitGameUserRateReq) returns(SubmitGameUserRateResp) {}
  // 获取用户开黑形象自评题目
  rpc GetGameUserSelfRateQuestions(GetGameUserSelfRateQuestionsReq) returns (GetGameUserSelfRateQuestionsResp) {}
  // 用户提交自评结果
  rpc SubmitGameUserSelfRate(SubmitGameUserSelfRateReq) returns (SubmitGameUserSelfRateResp) {}
  // 根据维度id获取绑定的玩法
  rpc GetTabByDimensionIds(GetTabByDimensionIdsReq) returns (GetTabByDimensionIdsResp) {}
  // 获取用户收到评价列表
  rpc GetGameUserBeRateList(GetGameUserBeRateListReq) returns(GetGameUserBeRateListResp) {}
  // 根据用户自定义输入内容重排标签
  rpc ReorderRateTags(ReorderRateTagsReq) returns (ReorderRateTagsResp) {}

  //////////////////////////// 客户端调用的接口 /////////////////////////////
  // 触发添加用户评价
  rpc AddGameUserRate(AddGameUserRateReq) returns(AddGameUserRateResp) {}
  // 预设可添加评价判断
  rpc CheckAddGameUserRate(CheckAddGameUserRateReq) returns(CheckAddGameUserRateResp) {}
  // 设置搭子IM聊天来源Token
  rpc BatchSetFirstChatToken(BatchSetFirstChatTokenReq) returns(BatchSetFirstChatTokenResp) {}
  // 设置搭子IM聊天来源Token
  rpc BatchGetFirstChatToken(BatchGetFirstChatTokenReq) returns(BatchGetFirstChatTokenResp) {}
  // 获取用户未评价数量
  rpc GetGameUserNotRateCount(GetGameUserNotRateCountReq) returns(GetGameUserNotRateCountResp) {}
  // 是否需要发送tt语音助手
  rpc IsNeedPushAssist(IsNeedPushAssistReq) returns(IsNeedPushAssistResp) {}
  // 获取用户开黑形象
  rpc GetGameUserPersonalImage(GetGameUserPersonalImageReq) returns(GetGameUserPersonalImageResp) {}
  // 获取搭子卡维度配置
  rpc GetGameDimensionConf(GetGameDimensionConfReq) returns (GetGameDimensionConfResp) {}

  rpc AddOtherRate(AddOtherRateReq) returns (AddOtherRateResp) {}
  rpc UnionScore(UnionScoreReq) returns (UnionScoreResp) {}

  // 获取点评展示与处罚配置
  rpc GetShowRateAndPunishConfWithCache(GetShowRateAndPunishConfReq) returns (GetShowRateAndPunishConfResp) {}
  // 获取用户评价信誉分数
  rpc GetUserReputationScore(GetUserReputationScoreReq) returns (GetUserReputationScoreResp) {}
}

enum GameUserRateSourceType{
  GAME_USER_RATE_SOURCE_TYPE_UNSPECIFIED = 0;
  GAME_USER_RATE_SOURCE_TYPE_CHANNEL = 1; // 房间
  GAME_USER_RATE_SOURCE_TYPE_IM_CHAT = 2; // IM聊天页
  GAME_USER_RATE_SOURCE_TYPE_ASSISTANT_PUSH = 3; // 语音助手推送
  GAME_USER_RATE_SOURCE_TYPE_CHANNEL_LETTER = 4; // 房间小信封来源
}

message GetGameUserRateByIdReq {
  string id = 1;
}

message GetGameUserRateByIdResp {
  GameUserRateItem data = 1;
}

message GetGameUserRateListReq {
  uint32 uid = 1;
  // 评价来源，互动提示信息可用于不同文案
  GameUserRateSourceType source = 2;
  // 用户id
  uint32 rate_uid = 3;
  // last id，分页用
  string last_id = 4;
}

message GetGameUserRateListResp {
  // 评价详情数据
  repeated GameUserRateItem data = 1;
  // 是否已经到底部，true为底部
  bool load_finish = 2;
}


enum GameUserRateStatus {
  GAME_USER_RATE_STATUS_UNSPECIFIED = 0;
  // 未评价
  GAME_USER_RATE_STATUS_NOT_RATE = 1;
  // 已评价
  GAME_USER_RATE_STATUS_RATED = 2;
  // 已过期
  GAME_USER_RATE_STATUS_EXPIRED = 3;
}

message GameUserRateItem {
  // id
  string id = 1;
  // 用户id
  uint32 uid = 2;
  // 被评价用户id
  uint32 rate_uid = 3;
  // 玩法id
  uint32 tab_id = 4;
  // 来源
  GameUserRateSourceType source = 5;
  // 评价创建时间， ms
  uint64 create_time = 6;
  // 交互提示信息，如：5小时前跟Ta一起玩过王者荣耀， 废弃
  string intro_tips = 7;
  // 评论状态， 1-未评价，2-已评价，3-已过期
  GameUserRateStatus status = 8;
  // 评价意向，赞或踩，0-未选，1-赞，2-踩
  uint32 attitude = 9;
  // 评价标签
  repeated GameUserRateTag tags = 10;
  // 用户自定义评价
  string user_rate_text = 11;
}

// 标签详情
message GameUserRateTag {
  // 维度标签id，普通标签无
  string id = 1;
  // 标签名称
  string name = 2;
  // 1-正向标签，点赞展示；2-负向标签，踩展示
  uint32 tag_type = 3;
}

message SubmitGameUserRateReq {
  // 评价id
  string id = 1;
  // 评价意向，赞或踩，0-未选，1-赞，2-踩
  uint32 attitude = 2;
  // 选择的评价标签
  repeated GameUserRateTag select_tags = 3;
  // 用户id
  uint32 uid = 4;
  // 用户自定义评价
  string user_rate_text = 5;
  // T盾审核是否通过
  bool is_shield_pass = 6;
  // 是否有风控风险
  bool has_risk = 7;
}
message SubmitGameUserRateResp {
  // 是否需要加经验
  bool is_need_add_exp = 1;
  // 成功评价后的信息
  GameUserRateItem item = 2;
  // 是否需要给被评价用户加经验
  bool is_rate_user_need_add_exp = 3;
}


message GetTabByDimensionIdsReq {
  // 维度id
  repeated string ids = 1;
}

message GetTabByDimensionIdsItem {
  repeated uint32 tab_ids = 1;
}

message GetTabByDimensionIdsResp {
  // k:维度id, v:玩法id
  map<string, GetTabByDimensionIdsItem> dimension_tab_map = 1;
}

message AddGameUserRateReq {
  uint32 uid = 1;
  repeated AddGameUserRateItem items = 2;
}

enum GameUserRateAddTagType {
  GAME_USER_RATE_ADD_TAG_TYPE_UNSPECIFIED = 0;
  // 房间标签
  GAME_USER_RATE_ADD_TAG_TYPE_CHANNEL = 1;
  // 搭子标签
  GAME_USER_RATE_ADD_TAG_TYPE_GAME_PAL = 2;
}

message AddGameUserRateItem {
  // see GameUserRateSourceType
  uint32 source = 1;
  // 被评价用户id
  uint32 rate_uid = 2;
  // 触发時間,ms
  uint64 interact_time = 3;
  // 玩法id
  uint32 tab_id = 4;
  // 需要添加的标签类型
  repeated GameUserRateAddTagType add_tag_type = 5;
}

message AddGameUserRateResp {
  repeated AddGameUserRateItem items = 2;
}

message CheckAddGameUserRateReq {
  uint32 uid = 1;
  string self_device_id = 2;
  repeated uint32 rate_uids = 3;
}

message CheckAddGameUserRateResp {
  // key:rate_uid, v:是否可以添加评价
  map<uint32, bool> base_filter = 1;
  // key:rate_uid, v:是否可以添加房间标签
  map<uint32, bool> channel_tags_filter = 2;
  // key:rate_uid, v:是否可以添加搭子标签
  map<uint32, bool> game_pal_tags_filter = 3;
}

message BatchSetFirstChatTokenReq {
  repeated SetFirstChatTokenReqItem items = 1;
}

message SetFirstChatTokenReqItem {
  uint32 uid = 1;
  uint32 rate_uid = 2;
  uint32 tab_id = 3;
}

message BatchSetFirstChatTokenResp {
}

message BatchGetFirstChatTokenReq {
  repeated GetFirstChatTokenReqItem items = 1;
}

message GetFirstChatTokenReqItem {
  uint32 uid = 1;
  uint32 rate_uid = 2;
}

message BatchGetFirstChatTokenResp {
  // key:uid_rateUid, value:tabId
  map<string, uint32> items = 1;
}

message GetGameUserNotRateCountReq {
  // 用户id
  uint32 uid = 1;
  // 最新已读评价的id, 6.49添加
  string rate_id = 2;
  // 最新已读评价的创建时间戳, 6.49添加
  uint64 create_time = 3;
}

message GetGameUserNotRateCountResp {
  // 未评价数量
  uint32 not_rate_count = 1;
  // 未读数量
  uint32 not_read_count = 2;
  // 最新的下发评价/收到评价时间戳
  int64 first_record_time = 4;
}

message IsNeedPushAssistReq {
  uint32 uid = 1;
}

message IsNeedPushAssistResp {
 bool result = 1;
}

message GetGameUserPersonalImageReq{
  repeated GameUserPersonalImageReqItem item = 1;
}

message GameUserPersonalImageReqItem {
  // 用户id
  uint32 uid = 1;
  // 玩法id
  uint32 tab_id = 2;
}

message GetGameUserPersonalImageResp{
  // key:uid_tabId, value:用户开黑形象
  map<string, GameUserPersonalImageRespItem>  items = 1;
}

message GameUserPersonalImageRespItem {
  // 维度分数
  repeated DimensionItem item = 1;
  // 评价总分
  float total_score = 2;
  // 开黑关键字，aigc返回
  string game_key_words = 3;
  // 关键字描述，aigc返回
  string game_key_words_desc = 4;
  // 自我介绍，aigc返回
  string self_intro = 5;
  // 相似用户占比，数据库写入时取10%-40%之间的随机数，H5测评结果用
  string similar_user_percent = 6;
}

message DimensionItem {
  // 维度id
  string id = 1;
  // 维度名称
  string name = 2;
  // 维度分数
  float score = 3;
}

// 自评问题配置
message H5Question {
  message Option {
    uint32 opt_id = 1;  // 选项id
    string content = 2; // 选项内容
  }
  // 题目id
  string question_id = 1;
  // 题目描述
  string description = 2;
  // 选项
  repeated Option options = 3;
  // 题目所属维度id
  string dimension_id = 4;
}

// 获取开黑形象自评题目
message GetGameUserSelfRateQuestionsReq {
  uint32 uid = 1;
  uint32 tab_id = 2;
}

message GetGameUserSelfRateQuestionsResp {
  repeated H5Question questions = 1;
}

message SelfRateResult {
  // 题目id
  string question_id = 1;
  // 选项id
  uint32 opt_id = 2;
  // 题目所属维度id
  string dimension_id = 3;
}

// 提交开黑形象自评结果
message SubmitGameUserSelfRateReq {
  uint32 uid = 1;
  uint32 tab_id = 2;
  repeated SelfRateResult results = 3;  // 答题结果
}

message SubmitGameUserSelfRateResp {
  GameUserPersonalImageRespItem item = 1;
}

message GetGameDimensionConfReq {
  repeated uint32 tab_id = 1;
}

message GameDimensionItem {
  repeated DimensionItem dimension_items = 1;
}

message GetGameDimensionConfResp {
  map<uint32, GameDimensionItem> items = 1;
  uint32 self_rate_internal = 2;  // 单位:小时
}

message AddOtherRateReq {
  repeated OtherRateMsg other_rate_msg = 1;
}

message OtherRateMsg{
    map<string,int32> dimension_score_map = 1;
    uint32 uid = 2;
    uint32 tab_id = 3;
    uint32 rate_uid = 4; //评价的人
    int64 happen_time = 5;
    // 用户搭子擦亮的时间
    int64 game_pal_light_time = 6;
}

message AddOtherRateResp {
}

message UnionScoreReq {
}

message DimensionScoreInfo {
    uint32 uid = 1;
    uint32 tab_id = 2;
    map<string,float> dimension_total_score_map = 3;
    string tab_name = 4;
    string gender = 5;
    string label = 6;
}

message UnionScoreResp {
    repeated DimensionScoreInfo dimension_score_infos = 1;
}

message GetGameUserBeRateListReq {
  uint32 uid = 1;
  // last record time，分页用
  uint64 last_record_time = 2;
}

message GetGameUserBeRateListResp {
  // 评价详情数据
  repeated GameUserBeRateItem items = 1;
  // 是否已经到底部，true为底部
  bool load_finish = 2;
}

message GameUserBeRateItem {
  // 好评记录item
  message LikeItem {
    // 评价id
    string id = 1;
    // 用户id
    uint32 uid = 2;
    // 下发时间
    int64 create_time = 3;
    // 评价标签
    repeated string select_tags = 4;
    // 用户自定义评价
    string user_rate_text = 5;

  }
  // 扣分记录item
  message DeductItem {
    // 评价id
    string id = 1;
    // 当前信誉分
    uint32 cur_score = 2;
    // 下发时间
    int64 create_time = 3;
    // 扣分详情
    repeated DeductDetail deduct_detail = 4;
  }

  // item类型，1-好评，2-扣分
  uint32 item_type = 1;
  oneof content {
    LikeItem like_item = 2; // item_type=1时取
    DeductItem deduct_item = 3; // item_type=2时取
  }
}

message DeductDetail {
  // 扣分标签名
  string tag_name = 1;
  // 扣分数
  uint32 score = 2;
}

message GetUserReputationScoreReq {
  uint32 uid = 1;
}

message GetUserReputationScoreResp {
// 信誉分
  uint32 score = 1;
}

// 根据用户自定义输入内容重排标签
message ReorderRateTagsReq {
  uint32 uid = 1;
  // 用户自定义评价
  string user_rate_text = 2;
  // 评价id
  string rate_item_id = 3;
}

message ReorderRateTagsResp {
  // 返回重排后的标签列表
  repeated GameUserRateTag rate_tags = 1;
}