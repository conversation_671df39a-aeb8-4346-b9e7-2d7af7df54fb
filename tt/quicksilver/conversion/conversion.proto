syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/conversion";
package conversion;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service ConversionServer {

    rpc Get<PERSON>ello (ConversionServerReq) returns (ConversionServerResp) {
    }

    rpc ConversionDebris ( ConversionDebrisReq )  returns ( ConversionDebrisResp ){

    }

    rpc AddConversionConfig ( AddConversionConfigReq ) returns ( AddConversionConfigResp ){

    }

    rpc SetConversionConfigs ( SetConversionConfigsReq ) returns ( SetConversionConfigsResp ){

    }

    rpc DelConversionConfig ( DelConversionConfigReq ) returns ( DelConversionConfigResp ){

    }

    rpc DelConversionConfByIds ( DelConversionConfByIdsReq ) returns ( DelConversionConfByIdsResp ){

    }

    rpc GetAllConversionConfig ( GetAllConversionConfigReq ) returns ( GetAllConversionConfigResp ){

    }

    rpc GetConversionLogs ( GetConversionLogsReq ) returns ( GetConversionLogsResp ){

    }

    rpc GetFriendGift ( GetFriendGiftReq ) returns ( GetFriendGiftResp ){

    }

    rpc AddComposeMaterialConf ( AddComposeMaterialConfReq ) returns ( AddComposeMaterialConfResp ){

    }

    rpc BatchAddComposeMaterialConf ( BatchAddComposeMaterialConfReq ) returns ( BatchAddComposeMaterialConfResp ){

    }

    rpc DelComposeMaterialConf ( DelComposeMaterialConfReq ) returns ( DelComposeMaterialConfResp ){

    }

    rpc GetAllComposeMaterialConf ( GetAllComposeMaterialConfReq ) returns ( GetAllComposeMaterialConfResp ){

    }

    rpc GetComposeGiftConfById ( GetComposeGiftConfByIdReq ) returns ( GetComposeGiftConfByIdResp ){

    }

    rpc AddComposeGiftConf ( AddComposeGiftConfReq ) returns ( AddComposeGiftConfResp ){

    }

    rpc BatchAddComposeGiftConf ( BatchAddComposeGiftConfReq ) returns ( BatchAddComposeGiftConfResp ){

    }

    rpc UpdateComposeGiftConf ( UpdateComposeGiftConfReq ) returns ( UpdateComposeGiftConfResp ){

    }

    rpc DelComposeGiftConf ( DelComposeGiftConfReq ) returns ( DelComposeGiftConfResp ){

    }

    rpc GetAllComposeGiftConf ( GetAllComposeGiftConfReq ) returns ( GetAllComposeGiftConfResp ){

    }

    rpc GetUserComposeMonthLogs ( GetUserComposeMonthLogsReq ) returns ( GetUserComposeMonthLogsResp ){

    }

    rpc GetUserComposeMonthLogsCnt ( GetUserComposeMonthLogsCntReq ) returns ( GetUserComposeMonthLogsCntResp ){

    }

    rpc GiftCompose ( GiftComposeReq ) returns ( GiftComposeResp ){

    }

    rpc AddDarkComposeGiftConf (AddDarkComposeGiftConfReq) returns (AddDarkComposeGiftConfResp) {}
    rpc DelDarkComposeGiftConf (DelDarkComposeGiftConfReq) returns (DelDarkComposeGiftConfResp) {}
    rpc GetAllDarkComposeGiftConf (GetAllDarkComposeGiftConfReq) returns (GetAllDarkComposeGiftConfResp) {}

    rpc AddDarkMaterialConf (AddDarkMaterialConfReq) returns (AddDarkMaterialConfResp) {}
    rpc DelDarkMaterialConf (DelDarkMaterialConfReq) returns (DelDarkMaterialConfResp) {}
    rpc GetAllDarkMaterialConf (GetAllDarkMaterialConfReq) returns (GetAllDarkMaterialConfResp) {}

    rpc GetDarkGift2Materials (GetDarkGift2MaterialsReq) returns (GetDarkGift2MaterialsResp) {}
    rpc GetDarkComposeGiftConfById(GetDarkComposeGiftConfByIdReq) returns (GetDarkComposeGiftConfByIdResp) {}
    rpc DarkGiftCompose (DarkGiftComposeReq) returns (DarkGiftComposeResp) {}
    rpc GetUserDarkComposeMonthLogs (GetUserDarkComposeMonthLogsReq) returns (GetUserDarkComposeMonthLogsResp) {}
    rpc GetUserDarkComposeMonthLogsCnt (GetUserDarkComposeMonthLogsCntReq) returns (GetUserDarkComposeMonthLogsCntResp) {}
    rpc CheckGiftComposeEntry (CheckGiftComposeEntryReq) returns (CheckGiftComposeEntryResp) {}


    //获取时间范围内的兑换订单数量和物品数量
    rpc GetBackpackItemUseCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}

    //获取时间范围内的兑换订单列表
    rpc GetBackpackItemUseOrder(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
}

enum LogType{
    CONVERSION = 0;
    SEND = 1;
    RECEIVE = 2;
}

message ConversionLog
{
    uint32 conversion_id = 1; //兑换ID，根据这个ID拿到兑换的信息
    uint32 conversion_time = 2; //兑换时间
    uint32 conversion_status = 3; //兑换状态  enum OrderStatus。
    uint32 conversion_log_type = 4;   //enum LogType 0兑换，1送出，2收礼
    uint32 count = 5;
}

message GetConversionLogsReq{
    uint32 uid = 1;
    uint32 page = 2;
    uint32 page_num = 3;
}

message GetConversionLogsResp{
    repeated ConversionLog conversion_logs = 1;
    uint32 total = 2;
}

message ConversionServerReq {
    //string a = 1;
}

message ConversionServerResp {
    //string b = 1;
}


///////////////////////////////

enum OrderStatus{
    Prepare_Type = 0;
    Commit_Type = 1;
    RollBack_Type = 2;
    GetGift_Type = 4; //收礼用户取操作
}

enum ConversionType {
    Gift_Type = 0; //礼物
    Horse_Type = 1; //坐骑
    Mic_Style = 2; //麦位框
    Dark_Gift = 3; //黑暗礼物
}

message UserItemInfo
{
    uint32 item_type = 1; //道具类型，
    uint32 use_item_id = 2; //用户数据库中对应的道具ID
    uint32 source_id = 3; //道具ID
    uint32 use_count = 4; //消耗数量
}

message ConversionDebris
{
    uint32 conversion_id = 1; //兑换类型
    repeated UserItemInfo user_item_list = 2; //消耗道具列表
    //uint32 conversion_type = 2; //兑换类型
}

//T豆包裹类型
enum PresentBusinessType
{
    E_PRESENT_BUSINESS_TYPE_DEFAULT = 0; // 默认业务类型
    E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT = 1; // 亲密礼物
}

//兑换关系结构
message ConversionConfig
{
    uint32 conversion_id = 1;
    map<uint32, uint32> debrisid_2_num = 2; //需要的碎片ID和数量
    string gift_id = 3;
    uint32 gift_num = 4;
    string gift_name = 5;
    uint32 conversion_type = 6; //兑换类型
    uint32 show_type = 7; //分类类型
    uint32 index = 8;    //排序号
    uint32 hold_day = 9; //持有天数
    bool is_delete = 10; //是否已经下架
    uint32 up_time = 11; //上架时间
    uint32 down_time = 12; //下架时间
    string note = 13; //备注
    //当前状态的枚举，分为未生效、上架中和已下架
    enum Status{
        Unknown = 0;
        NotActive = 1;
        Active = 2;
        Expired = 3;
    }
    Status status = 14;
    string gift_content = 15; // 奖励内容
    enum GiftType{
        Normal = 0;
        TimeLimit = 1;
    }
    GiftType gift_type = 16; // 礼物类型，普通/限时
    uint32 business_type = 17; //T豆包裹类型 see BusinessType
}

//兑换碎片
message ConversionDebrisReq
{
   uint32 uid  = 1;
   ConversionDebris conversion = 2;
   uint32 price = 3; //兑换得到礼物的价值，用于统计
   uint32 target_uid =4; //如果是送礼，送礼对象UID
   uint32 count = 5; //兑换次数
}

message ConversionDebrisResp
{
    string order_id = 1;
}

//添加兑换配置
message AddConversionConfigReq 
{
    ConversionConfig conversion_config = 1;
}

message AddConversionConfigResp
{
}

message SetConversionConfigsReq
{
    repeated ConversionConfig configs = 1;
}

message SetConversionConfigsResp
{

}

//删除兑换配置
message DelConversionConfigReq
{
    uint32 conversion_id = 1;
}

message DelConversionConfigResp
{

}

message DelConversionConfByIdsReq{
    repeated uint32 conversion_id_list = 1;
    uint32 effect_time = 2;     // 定时删除生效时间戳(s)
}

message DelConversionConfByIdsResp{

}

//取全部兑换配置
message GetAllConversionConfigReq
{

}

message GetAllConversionConfigResp
{
    repeated ConversionConfig conversion_configs = 1;
}

message GetFriendGiftReq
{
    uint32 uid = 1;
    string order_id = 2;
}

message GetFriendGiftResp
{
}


// 合成 原料配置
message ComposeMaterialConf
{
    uint32 id = 1;        // 唯一id
    uint32 gift_id = 2;   // 礼物id
    uint32 sort_id = 3;   // 排序id（大的排在前）
    uint32 update_at = 4;
    uint32 bg_id = 5;     // 包裹id
    string gift_name = 6; // 礼物名称
    uint32 gift_price = 7; // 礼物价格
}

// 新增原料配置
message AddComposeMaterialConfReq
{
    ComposeMaterialConf conf = 1;
}

message AddComposeMaterialConfResp{}

// 批量新增原料配置
message BatchAddComposeMaterialConfReq
{
    repeated ComposeMaterialConf conf_list = 1;
}

message BatchAddComposeMaterialConfResp{}

// 删除原料配置
message DelComposeMaterialConfReq
{
    uint32 id = 1;
}

message DelComposeMaterialConfResp{}

// 获取原料配置列表
message GetAllComposeMaterialConfReq{
}

message GetAllComposeMaterialConfResp
{
    repeated ComposeMaterialConf conf_list = 1;
}


// 合成 礼物配置
message ComposeGiftConf
{
    enum GiftType {
        Common = 0;     // 普通
        TimeLimit = 1;  // 限时
    }
    uint32 id = 1;        // 合成id
    uint32 bg_id = 2;     // 包裹id
    uint32 gift_id = 3;   // 该包裹中的礼物id
    uint32 gift_type = 4; // see GiftType
    uint32 sort_id = 5;   // 排序id（大的排在前）
    uint32 up_time = 6;   // 上架时间（仅当gift_type=TimeLimit时有效）
    uint32 down_time = 7; // 下架时间（仅当gift_type=TimeLimit时有效）
    uint32 update_at = 8; // 配置时间
    string gift_name = 9; // 包裹内的礼物名称
    string note = 10;     // 备注
    //当前状态的枚举，分为未生效、上架中和已下架
    enum Status{
        Unknown = 0;
        NotActive = 1;
        Active = 2;
        Expired = 3;
    }
    Status status = 11;
    uint32 business_type = 12; //T豆包裹类型 see BusinessType
    uint32 activity_id = 13; // 活动id，仅用于后台
    string operate_user = 14; // 操作人, 仅用于后台
}

// 新增合成礼物配置
message AddComposeGiftConfReq
{
    ComposeGiftConf conf = 1;
}

message AddComposeGiftConfResp{}

// 批量新增合成礼物配置
message BatchAddComposeGiftConfReq
{
    repeated ComposeGiftConf conf_list = 1;
}

message BatchAddComposeGiftConfResp{}

// 新增合成礼物配置
message UpdateComposeGiftConfReq
{
    ComposeGiftConf conf = 1;
}

message UpdateComposeGiftConfResp{}

// 删除合成礼物配置
message DelComposeGiftConfReq
{
    uint32 id = 1;  // 合成id
}

message DelComposeGiftConfResp{}

// 获取合成礼物配置列表
message GetAllComposeGiftConfReq{
    uint32 uid = 1;
}

message GetAllComposeGiftConfResp
{
    repeated ComposeGiftConf conf_list = 1;
}

// 获取合成礼物配置
message GetComposeGiftConfByIdReq{
    uint32 uid = 1;
    uint32 id = 2;  // 合成id
}

message GetComposeGiftConfByIdResp
{
    ComposeGiftConf conf = 1;
}

// 合成记录
message ComposeLog
{
    enum Status {
        Init = 0;
        Success = 1;
        Fail = 2;
    }

    string order_id = 1;
    uint32 uid = 2;
    uint32 bg_id = 3;               // 包裹id
    uint32 num = 4;
    uint32 status = 5;              // see Status
    string gift_desc = 6;           // 合成礼物描述
    uint32 gift_price = 7;          // 合成礼物价值
    string gift_icon_url = 8;       // 合成礼物图标url
    string material_used_desc = 9;  // 合成原料描述
    uint32 material_total_price = 10;// 合成原料总价值
    uint32 create_at = 11;          // 订单时间
}

// 获取用户合成记录
message GetUserComposeMonthLogsReq
{
    uint32 uid = 1;
    uint32 query_month_time = 2; // 记录所在的时间（用来确定查哪个月的记录）
    uint32 status = 3; // see ComposeLog::Status
    uint32 begin = 4;
    uint32 limit = 5;
}

message GetUserComposeMonthLogsResp
{
    repeated ComposeLog logs = 1;
}

message GetUserComposeMonthLogsCntReq
{
    uint32 uid = 1;
    uint32 query_month_time = 2; // 记录所在的时间（用来确定查哪个月的记录）
    uint32 status = 3; // see ComposeLog::Status
}

message GetUserComposeMonthLogsCntResp
{
    uint32 cnt = 1;
}

message ComposeGiftInfo
{
    uint32 bg_id = 1;           // 合成的礼物包裹id
    uint32 gift_id = 2;
    string gift_name = 3;       // 礼物名
    uint32 price = 4;           // 礼物价值
    string gift_icon_url = 5;   // 礼物图标url
    uint32 num = 6;
}

message MaterialUseInfo
{
    uint32 gift_id = 1;
    uint32 num = 2;
    string gift_name = 3;
    uint32 price = 4;           // 单价
    uint32 user_item_id = 5;    // 用户数据库中对应的道具ID
}

// 礼物合成接口
message GiftComposeReq
{
    uint32 uid = 1;
    ComposeGiftInfo compose_gift_info = 2;             // 合成的礼物信息
    repeated MaterialUseInfo material_list = 3; // 使用的原料列表
}

message GiftComposeResp{}

// =========================================== 运营后台黑暗礼物相关接口 start =================//
// 合成玩法类型
enum ComposeType {
    Common = 0;       // 普通合成礼物
    DarkCompose = 1;  // 黑暗礼物合成系列
}

// 原材料类型
enum MaterialType {
    GiftType = 0;     //礼物
    FragmentType = 1; //碎片
}

// 合成 原料配置
message DarkComposeMaterialConf
{
    uint32 id = 1;           // 唯一id
    uint32 gift_id = 2;      // 礼物/碎片id
    string gift_name = 3;    // 名称
    uint32 sort_id = 4;      // 排序id（大的排在前）
    uint32 update_at = 5;
    uint32 gift_price = 6;  
    uint32 compose_type = 7; // ComposeType
    uint32 item_type = 8;    // MaterialType
}

// 合成 礼物配置
message DarkComposeGiftConf
{
    enum GiftType {
        Common = 0;     // 普通
        TimeLimit = 1;  // 限时
    }
    uint32 id = 1;             // 合成id
    uint32 bg_id = 2;          // 包裹id
    uint32 gift_id = 3;        // 该包裹中的礼物id
    uint32 gift_type = 4;      // see GiftType
    uint32 sort_id = 5;        // 排序id（大的排在前）
    uint32 up_time = 6;        // 上架时间（仅当gift_type=TimeLimit时有效）
    uint32 down_time = 7;      // 下架时间（仅当gift_type=TimeLimit时有效）
    uint32 update_at = 8;      // 配置时间
    uint32 compose_type = 9;   // ComposeType

    repeated uint32 material_list = 10; //可用原材料
    bool is_goddess = 11;     // 是否为黑暗女神
    uint32 business_type = 12; //T豆包裹类型 see BusinessType
}

// 新增合成礼物配置
message AddDarkComposeGiftConfReq
{
    DarkComposeGiftConf conf = 1;
    repeated uint32 materials_list = 2;  // 可用原材料列表
}
message AddDarkComposeGiftConfResp{}

// 删除合成礼物配置
message DelDarkComposeGiftConfReq
{
    uint32 id = 1;  // 合成id
}
message DelDarkComposeGiftConfResp{}

// 获取合成礼物配置列表
message GetAllDarkComposeGiftConfReq{
    uint32 uid = 1;
}
message GetAllDarkComposeGiftConfResp
{
    repeated DarkComposeGiftConf conf_list = 1;
}

// 新增原料配置
message AddDarkMaterialConfReq
{
    DarkComposeMaterialConf conf = 1;
}
message AddDarkMaterialConfResp{}

// 删除原料配置
message DelDarkMaterialConfReq
{
    uint32 id = 1; //唯一id
}
message DelDarkMaterialConfResp{}

// 获取原料配置列表
message GetAllDarkMaterialConfReq{
    uint32 compose_type = 1;   // ComposeType
}
message GetAllDarkMaterialConfResp
{
    repeated DarkComposeMaterialConf conf_list = 1;
}
// =============================================运营后台黑暗礼物相关接口 end =================//
// 获取合成礼物配置
message GetDarkComposeGiftConfByIdReq{
    uint32 uid = 1;
    uint32 id = 2;  // 合成id
}

message GetDarkComposeGiftConfByIdResp
{
    DarkComposeGiftConf conf = 1;
}

// 获取指定合成礼物的原料配置列表
message GetDarkGift2MaterialsReq
{
    uint32 uid = 1;
    uint32 compose_id =2;
}

message GetDarkGift2MaterialsResp
{
    repeated DarkComposeMaterialConf conf_list = 1;
}


message DarkComposeGiftInfo
{
    uint32 compose_id = 1;
    uint32 bg_id = 2;           // 合成的礼物包裹id
    uint32 gift_id = 3;
    string gift_name = 4;       // 礼物名
    uint32 price = 5;           // 礼物价值
    string gift_icon_url = 6;   // 礼物图标url
    uint32 num = 7;
}

message DarkMaterialUseInfo
{
    uint32 gift_id = 1;
    uint32 num = 2;
    string gift_name = 3;
    uint32 price = 4;           // 单价
    uint32 user_item_id = 5;    // 用户数据库中对应的道具ID
    uint32 gift_type =6;        //原材料类型
}

message DarkGiftComposeReq
{
    uint32 uid = 1;
    uint32 compose_id = 2;
    DarkComposeGiftInfo compose_gift_info = 3;      // 合成的礼物信息
    repeated DarkMaterialUseInfo material_list = 4; // 使用的原料列表
    uint32 market_id = 5;                           //MarketId
}
message DarkGiftComposeResp{}

// 获取用户合成记录
message GetUserDarkComposeMonthLogsReq
{
    uint32 uid = 1;
    uint32 query_month_time = 2; // 记录所在的时间（用来确定查哪个月的记录）
    uint32 status = 3; // see ComposeLog::Status
    uint32 begin = 4;
    uint32 limit = 5;
}

message GetUserDarkComposeMonthLogsResp
{
    repeated ComposeLog logs = 1;
}

message GetUserDarkComposeMonthLogsCntReq
{
    uint32 uid = 1;
    uint32 query_month_time = 2; // 记录所在的时间（用来确定查哪个月的记录）
    uint32 status = 3; // see ComposeLog::Status
}

message GetUserDarkComposeMonthLogsCntResp
{
    uint32 cnt = 1;
}

// 检查合成入口是否开启
message CheckGiftComposeEntryReq {
    uint32 uid = 1;
}
  
message CheckGiftComposeEntryResp {
    bool is_open = 1;                // 是否开启入口
    uint32 materials_tol_price = 2;  // 背包材料可用于合成黑暗女神的总T豆值
    uint32 pool_full_value = 3;      // 水池蓄满值，单位T豆
}

// end黑暗系