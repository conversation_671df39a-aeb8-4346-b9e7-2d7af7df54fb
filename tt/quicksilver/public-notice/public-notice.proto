syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/public-notice";

package public_notice;

service PublicNotice {
    rpc PushBreakingNews(PushBreakingNewsReq) returns (PushBreakingNewsResp) {}
    rpc TestPushRichTextBreakingNews(TestPushRichTextBreakingNewsReq) returns (TestPushRichTextBreakingNewsResp) {}


		// =================== 运营后台相关 ==========================
	  rpc AddBreakingNewsConfig(AddBreakingNewsConfigReq) returns (AddBreakingNewsConfigResp) {}
		rpc UpdateBreakingNewsConfig(UpdateBreakingNewsConfigReq) returns (UpdateBreakingNewsConfigResp) {}
	 	rpc BatchDelBreakingNewsConfig(BatchDelBreakingNewsConfigReq) returns (BatchDelBreakingNewsConfigResp) {}
		rpc BatchGetBreakingNewsConfig(BatchGetBreakingNewsConfigReq) returns (BatchGetBreakingNewsConfigResp) {}

	// ===================== 房间播报相关 ==========================
	rpc AddChannelBreakingNewsConfig(AddChannelBreakingNewsConfigReq) returns (AddChannelBreakingNewsConfigResp) {}
	rpc UpdateChannelBreakingNewsConfig(UpdateChannelBreakingNewsConfigReq) returns (UpdateChannelBreakingNewsConfigResp) {}
	rpc BatchDelChannelBreakingNewsConfig(BatchDelChannelBreakingNewsConfigReq) returns (BatchDelChannelBreakingNewsConfigResp) {}
	rpc BatchGetChannelBreakingNewsConfig(BatchGetChannelBreakingNewsConfigReq) returns (BatchGetChannelBreakingNewsConfigResp) {}
	// ===================== 房间播报相关 ==========================


	  // =================== 置顶公告相关 ==========================
		rpc AddStickBreakingNews(AddStickBreakingNewsReq) returns (AddStickBreakingNewsResp) {}
		rpc DelStickBreakingNews(DelStickBreakingNewsReq) returns (DelStickBreakingNewsResp) {}
		rpc UpdateStickBreakingNews(UpdateStickBreakingNewsReq) returns (UpdateStickBreakingNewsResp) {}
	  // 获取全部置顶公告记录
		rpc GetAllStickBreakingNews(GetAllStickBreakingNewsReq) returns (GetAllStickBreakingNewsResp) {}
	  // 检查是否能操作置顶公告
		rpc CheckIfCouldOperateStickBreakingNews(CheckIfCouldOperateStickBreakingNewsReq) returns (CheckIfCouldOperateStickBreakingNewsResp) {}
		// =================== 置顶公告相关 ==========================
	  // 通用全服推送
		rpc CommonPublicMsgPush(CommonPublicMsgPushReq) returns (CommonPublicMsgPushResp) {}

        // ================== 房间活动通用能力相关 ==================
        // 发送活动房间公屏通知
        rpc SendActivityChannelIm(SendActivityChannelImReq) returns (SendActivityChannelImResp) {}
	  // 推送房间内小横幅（中奖）
		rpc PushBreakingNewsInRoom(PushBreakingNewsInRoomReq) returns (PushBreakingNewsInRoomResp) {}
}

// 通用全服推送
message CommonPublicMsgPushReq {
	bytes opt_data = 1; // 自定义pb序列化数据
	uint32 announce_scope = 2;     //公告范围      see enum: ANNOUNCE_SCOPE
	uint32 cmd_type = 3; // 推送命令号 see push_.proto CMD_TYPE
}

message CommonPublicMsgPushResp {
}

message PushBreakingNewsReq {
        // buf:lint:ignore ENUM_PASCAL_CASE
	enum BREAKING_CMD_TYPE {
		COMMON_BREAKING_EVENT_V3 = 0; // 通用全服公告
		SMASH_EGG_BREAKING = 1; // 魔力转转全服公告
	}
	BREAKING_CMD_TYPE breaking_cmd_type = 1;
	CommonBreakingNewsV3 common_breaking_news = 2;
	SmashEggBreakingNews smash_egg_breaking_news = 3;
	RichTextNews rich_text_news = 4; // 富文本全服扩展字段
}

message RichTextNews {
	uint32 news_id = 1;
	string custom_biz_name = 2; // 业务自定的数据 例如 [icon]相亲牵手lv5
	string custom_biz_level_icon = 3;
	string custom_biz_level_name = 4;
	string custom_biz_num = 5;
}

message PushBreakingNewsResp {

}

// 魔力转转大事件
message SmashEggBreakingNews
{
    uint32 current_hits    = 1;
    uint32 morph_hits    	= 2; 
	uint32 morph_flag    	= 3;  
	uint32 morph_end_time  = 4;
}

message UserInfo
{
	string account    = 1;
	string nick       = 2;
}

message ChannelInfo
{
	uint32 channel_displayid = 1;    // 涉及大事件的 房间displya_ID
	uint32 channel_bindid = 2;       // 涉及大事件的 房间bind ID
	uint32 channel_type = 3;        // 涉及大事件的 房间类型
	string channel_name = 4;        // 涉及大事件的 房间名称
	string channel_icon_md5 = 5; 	//房间头像md5
}

message GuildInfo
{
	string guild_name = 1;        // 公会名称
	uint32 guild_display_id = 2;  // 公会靓号id
}

//通用全服大事件V3
message CommonBreakingNewsV3
{
	uint32 from_uid        = 1;
	UserInfo from_user_info = 2;   // 可选，如果为空，则由本服务查询

	uint32 target_uid      = 3;
	UserInfo target_user_info = 4;  // 可选，如果为空，则由本服务查询

	uint32 channel_id = 5;    
	ChannelInfo channel_info = 6;	// 可选，如果为空，则由本服务查询

	uint32 guild_id = 7; 
	GuildInfo guild_info = 8;		// 可选，如果为空，则由本服务查询

	string news_content = 9;		 // 涉及大事件的 文案

	string jump_url = 10;           // 跳转URL
	uint32 delay_secs = 11;		 // 延迟n秒后再播放大事件

	string news_prefix = 12;		 // 文案前缀， 通用前缀则不用带 （"妈耶！", "哇塞！", "啊，碉堡了~", "牛批！", "号外~号外~", "敲厉害的！", "爆炸新闻！", "啊啊啊啊！", "哇~人生赢家！"）
	string dating_scene_name = 13;    // 相亲场景 

	CommBreakingNewsBaseOpt breaking_news_base_opt = 14;    //通用全服公告的一些基本属性
	PresentBreakingNewsBaseOpt present_news_base_opt = 15;   //送礼大事件的一些基本属性

	uint32 rich_level = 16;            //财富等级

	uint32 is_old_deal = 17;           //旧版客户端是否推送新的全服通知，旧版处理的话可以推送旧版文案内容 0：不处理                                      1：处理
	string old_news_content = 18;      //旧版文案内容

	uint32 tag_id = 19;        // 房间所在分类tag的tag_id

	uint32 nobility_level = 20; // 贵族等级
	string nobility_level_name = 21; //贵族等级名
	bool need_monster_info = 22; // 是否需要打龙信息
	uint32 nobility_extent_cnt = 23; // 贵族神王第几次延长
	bytes opt_data = 24;	// 自定义pb序列化数据
	string hard_url = 25;    // 支持区分不同马甲包链接，不为空时由客户端拼接域名进行跳转， 为空时使用jump_url跳转
	bool is_wealth_god_breaking_news = 26; // 是否是财神公告（部分房间需要屏蔽）
}

message CommBreakingNewsBaseOpt
{
    uint32 trigger_type = 1;       //触发条件      see enum: TRIGGER_TYPE
	uint32 rolling_count = 2;      //滚动次数  
	uint32 rolling_time = 3;       //每次滚动时长          sec
	uint32 announce_scope = 4;     //公告范围      see enum: ANNOUNCE_SCOPE
	uint32 announce_position = 5;  //公告位置      see enum: ANNOUNCE_POSITION
    uint32 jump_type = 6;          //点击跳转类型        see enum: JUMP_TYPE
    uint32 jump_position = 7;      //跳转的位置       see  enum: JUMP_POSITION
}

//送礼的一些基本属性
message PresentBreakingNewsBaseOpt
{
    string gift_name = 1;         // 涉及大事件的 礼物名称
	uint32 gift_id = 2;           // 涉及大事件的 礼物ID
	uint32 gift_count = 3;       //涉及大事件的 礼物数量
	string gift_icon_url = 4;    //涉及大事件的 礼物缩略图url
	uint32 gift_worth = 5;       //  涉及大事件的 礼物价值
 	uint32 magic_id = 6;       //  幸运礼物 - 开出该礼物的幸运精灵id
    string magic_name = 7;       //  幸运礼物 - 开出该礼物的幸运精灵名称
  	string magic_icon = 8;       //  幸运礼物 - 开出该礼物的幸运精灵icon
}

// rush机制信息
message RushInfo
{   
   uint32 rush_type = 1; // see RushType
   uint32 rush_max_rand_ts = 2; //rush 最大随机值 秒
   uint32 rush_wait_ts = 3; //  rush 最大等待时间 秒
}

message TestPushRichTextBreakingNewsReq {
	uint32 news_id = 1;
	uint32 from_uid = 2;
	uint32 to_uid = 3;
	uint32 channel_id = 4;
	uint32 gift_id = 5;
	string custom_jump_link = 6;
	string custom_biz_name = 7; // 业务自定的数据 例如 [icon]相亲牵手lv5
	string custom_biz_icon = 8;
	string custom_biz_level = 9;
	string custom_biz_num = 10;
	uint32 guild_id = 11;
}
message TestPushRichTextBreakingNewsResp {}

// =============== 运营后台相关(动态配置富文本全服公告) ================

message ChannelBreakingNewsConfig {
	uint32 news_id = 1;
	string news_name = 2;
	string news_template = 3; // 富文本模板 包含各个业务信息占位符 如：{{user.nickname}} {{gift.gift_name}}
	uint32 rolling_count = 4;      //滚动次数
	uint32 rolling_time = 5;       //每次滚动时长          sec
	string main_body_font_color = 6; 	 //正文色值
	string name_font_color = 7;   //名称字体色值
	string font_shadow_color = 8; 			 //字体投影色值
	uint32 margin_left = 9; // 左边距
	uint32 anime_type = 10; // 动画类型 see enum: ANIME_TYPE
	string anime_zip = 11; // 动画压缩包
	string anime_md5 = 12; // 动画md5
	string anime_preview = 13; //动画预览
	string bg_picture_url = 14; // 底图
}

message AddChannelBreakingNewsConfigReq {
	ChannelBreakingNewsConfig channel_breaking_news_config = 1;
}

message AddChannelBreakingNewsConfigResp {}

message UpdateChannelBreakingNewsConfigReq{
	ChannelBreakingNewsConfig channel_breaking_news_config = 1;
}

message UpdateChannelBreakingNewsConfigResp{}

message BatchDelChannelBreakingNewsConfigReq {
	repeated uint32 news_id_list = 1;
}

message BatchDelChannelBreakingNewsConfigResp {}

message BatchGetChannelBreakingNewsConfigReq {
	uint32 search_news_id = 1; // 搜索ID
	string search_keyword = 2; // 搜索关键字
}

message BatchGetChannelBreakingNewsConfigResp {
	repeated ChannelBreakingNewsConfig channel_breaking_news_config_list = 1;
}


message BreakingNewsConfig {
        // buf:lint:ignore ENUM_PASCAL_CASE
	enum NEWS_INFO_TYPE {
		NEWS_INFO_TYPE_UNKNOWN = 0;
		NEWS_INFO_TYPE_CUSTOM = 100;				// 自定义
		NEWS_INFO_TYPE_CUSTOM_TEXT = 101;
		NEWS_INFO_TYPE_CUSTOM_LEVEL_ICON = 102; //等级图标
		NEWS_INFO_TYPE_CUSTOM_LEVEL_NAME = 103; //等级名称
		NEWS_INFO_TYPE_CUSTOM_NUM = 104;				 //数量
		NEWS_INFO_TYPE_CUSTOM_JUMP_LINK = 105;	 // 去围观跳转短链
		NEWS_INFO_TYPE_USER_INFO = 200;	 	// 用户信息
		NEWS_INFO_TYPE_USER_FROM_ACCOUNT = 201;
		NEWS_INFO_TYPE_USER_TO_ACCOUNT = 202;
		NEWS_INFO_TYPE_USER_FROM_NICKNAME = 203;
		NEWS_INFO_TYPE_USER_TO_NICKNAME = 204;
		NEWS_INFO_TYPE_USER_FROM_HEAD = 205;
		NEWS_INFO_TYPE_USER_TO_HEAD = 206;
		NEWS_INFO_TYPE_USER_FROM_UKW_ACCOUNT = 207;
		NEWS_INFO_TYPE_USER_TO_UKW_ACCOUNT = 208;
		NEWS_INFO_TYPE_CHANNEL = 300; 			// 房间
		NEWS_INFO_TYPE_CHANNEL_NAME = 301;
		NEWS_INFO_TYPE_CHANNEL_ID = 302;
		NEWS_INFO_TYPE_GIFT = 400; 	// 礼物
		NEWS_INFO_TYPE_GIFT_ICON = 401; // 礼物图标
		NEWS_INFO_TYPE_GIFT_NAME = 402; // 礼物名称
		NEWS_INFO_TYPE_GIFT_PRICE = 403; // 礼物价格
		NEWS_INFO_TYPE_GUILD = 500; 			//公会
		NEWS_INFO_TYPE_GUILD_ICON = 501; //公会图标
		NEWS_INFO_TYPE_GUILD_NAME = 502; //公会名称
		NEWS_INFO_TYPE_GUILD_ID = 503; // 公会ID

		NEWS_INFO_TYPE_FULL_TEXT_OUTER = 9999; // 富文本模板外壳
	}

        // buf:lint:ignore ENUM_PASCAL_CASE
	enum ANIME_TYPE {
		ANIME_TYPE_UNKNOWN = 0;
		ANIME_TYPE_LOTTIE = 1;
		ANIME_TYPE_VAP = 2;
		ANIME_TYPE_LOTTIE_FUSION = 3;
		ANIME_TYPE_VAP_FUSION = 4;
		ANIME_TYPE_PNG = 5;
	}

	//公告范围       枚举值采用二进制的向左移一位方式                 1(1), 10(2), 100(4)
        // buf:lint:ignore ENUM_PASCAL_CASE
	enum ANNOUNCE_SCOPE {
		ANNOUNCE_SCOPE_UNKNOWN = 0;
		INSIDE_CHANNEL = 1;              //房间内
		OUTSIDE_CHANNEL = 2;             //房间外
		PUBLIC_GUILD_CHANNEL = 4;        //公会公开房
		THIS_CHANNEL = 8;                //本房间内
	}

	//公告位置
        // buf:lint:ignore ENUM_PASCAL_CASE
	enum ANNOUNCE_POSITION {
		ANNOUNCE_POSITION_UNKNOWN = 0;
		UPPER = 1;           //房间内的上面
		MIDDLE = 2;         //房间内的中间
	}


	//点击跳转类型        3:房间内外     枚举值采用二进制的向左移一位方式                 1(1), 10(2), 100(4)
        // buf:lint:ignore ENUM_PASCAL_CASE
	enum JUMP_TYPE  {
		NO_JUMP_CLICK  = 0;                  //点击不跳转
		JUMP_CLICK_INSIDE = 1;               //房间内点击可跳转
		JUMP_ClICK_OUTSIDE = 2;              //房间外点击可跳转
	}

	uint32 news_id = 1;
	string news_name = 2;
	repeated NewsTemplateItem news_template_list = 3; // 富文本模板, 一个标签一个元素
	uint32 announce_scope = 5;     //公告范围      see enum: ANNOUNCE_SCOPE
	uint32 rolling_count = 6;      //滚动次数
	uint32 rolling_time = 7;       //每次滚动时长          sec
	uint32 jump_type = 8;          //点击跳转类型        see enum: JUMP_TYPE
	string main_body_font_color = 9; 	 //正文色值
	string name_font_color = 10;   //名称字体色值
	string font_shadow_color = 11; 			 //字体投影色值
	bool show_to_circusee = 12; 	// 是否去围观
	string to_circusee_font_color = 13; //去围观字体色值
	string to_circusee_shadow_color = 14; //去围观投影色值
	uint32 margin_left = 15; // 左边距
	uint32 anime_type = 16; // 动画类型 see enum: ANIME_TYPE
	string anime_zip = 17; // 动画压缩包
	string anime_md5 = 21; // 动画md5
	string anime_preview = 18; //动画预览
	bool is_head_nick_jump = 19; //头像昵称是否可以跳转
	string custom_jump = 20; // 自定义跳转(去围观短链)
	string go_to_watch_text = 22; // 去围观文案
	uint32 new_chance_game_type = 23; // 新玩法类型 see enum: NewChanceGameType
	uint32 activity_id = 24; // 活动id
	bool is_mask_nickname = 25; // 是否需要遮罩昵称
}


// 用户字段
message UserField {
	string nickname = 1; // 昵称
	string face_md5 = 2; // 头像md5
}

// 文本字段
message TextField {
	string text = 1;
}

// 礼物字段
message GiftField {
	string gift_name = 1; // 礼物名称
	string gift_icon = 2; // 礼物图标
	uint32 gift_price = 3; // 礼物价格
	uint32 gift_cnt = 4; // 礼物数量
}



message NewsTemplateItem {
	repeated uint32 news_info_type_list = 1; // 模板信息类型 see NEWS_INFO_TYPE
	string template = 2; // 富文本标签模板
	string value = 3; // 自定义值
}

message AddBreakingNewsConfigReq {
	BreakingNewsConfig breaking_news_config = 1;
}

message AddBreakingNewsConfigResp {}

message UpdateBreakingNewsConfigReq{
	BreakingNewsConfig breaking_news_config = 1;
}

message UpdateBreakingNewsConfigResp{}

message BatchDelBreakingNewsConfigReq {
	repeated uint32 news_id_list = 1;
}

message BatchDelBreakingNewsConfigResp {}

message BatchGetBreakingNewsConfigReq {
	uint32 search_news_id = 1; // 搜索ID
	string search_keyword = 2; // 搜索关键字
}

message BatchGetBreakingNewsConfigResp {
	repeated BreakingNewsConfig breaking_news_config_list = 1;
}

// =================== 置顶公告相关 ==========================
message AddStickBreakingNewsReq {
	uint32 news_id = 1; // 公告ID
	float rank = 2; // 排序
	int64 begin_time = 3; // 开始时间
	int64 end_time = 4; // 结束时间
}

//
message AddStickBreakingNewsResp {
}

// 删除置顶公告请求
message DelStickBreakingNewsReq {
	uint32 record_id = 1; // 记录ID
}

// 删除置顶公告返回
message DelStickBreakingNewsResp {
}


// 更新置顶公告请求
message UpdateStickBreakingNewsReq {
	uint32 record_id = 1; // 记录ID
	float rank = 2; // 排序
	int64 begin_time = 3; // 开始时间
	int64 end_time = 4; // 结束时间
}

// 更新置顶公告返回
message UpdateStickBreakingNewsResp {
}


// 获取全部置顶公告记录
message  GetAllStickBreakingNewsReq {
}

// 置顶公告状态
enum StickStatus {
	STICK_STATUS_UNSPECIFIED = 0;
	STICK_STATUS_WAITING = 1; // 待生效
	STICK_STATUS_EFFECTIVE = 2; // 生效中
	STICK_STATUS_EXPIRED = 3; // 已过期
}

// 置顶公告
message StickBreakingNews {
	uint32 record_id = 1; // 记录ID
	uint32 news_id = 2; // 公告ID
	string news_name = 3; // 公告名称
	string anime_name = 4; // 动效名称
	string anime_zip = 5; // 动效zip
	uint32 anime_type = 6; // 动效类型 see enum: ANIME_TYPE
	string anime_preview = 7; // 动效预览
	string rank = 8; // 排序 web端使用string 保证精度统一
	int64 begin_time = 9; // 开始时间
	int64 end_time = 10; // 结束时间
	StickStatus status = 11; // 状态
	bool is_permanent = 12; // 是否永久置顶 永久置顶不展示时间
}

// 获取置顶公告返回
message GetAllStickBreakingNewsResp {
	repeated StickBreakingNews stick_breaking_news_list = 1;
}

// 检查是否能操作置顶公告请求
message CheckIfCouldOperateStickBreakingNewsReq {
	uint32 news_id = 1; // 公告ID add时使用
	uint32 record_id = 2; // 记录ID update时使用
	int64 begin_time = 3; // 开始时间
	int64 end_time = 4; // 结束时间
}

// 失败直接接口报错
message CheckIfCouldOperateStickBreakingNewsResp {
}

// 推送房间内小横幅（中奖）
message PushBreakingNewsInRoomReq {
	uint32 news_id = 1;
	uint32 channel_id = 2;
	uint32 uid = 3;
	uint32 gift_id = 4;
	uint32 gift_cnt = 5;
}

message PushBreakingNewsInRoomResp {
}

// =================== 置顶公告相关 ==========================


// ================== 房间活动通用能力相关 ==================

// 发送活动房间公屏通知
message SendActivityChannelImReq{
    uint32 activity_id = 1; // 活动ID
    repeated uint32 cid_list = 2; // 房间ID列表
    bool all_channel = 3;   // 是否全房间
    string content = 4;     // 通知内容
    string icon = 6;        // 通知图标
    string jump_url = 7;   // 跳转链接
}

message SendActivityChannelImResp {
}