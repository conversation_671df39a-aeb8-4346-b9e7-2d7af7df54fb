syntax = "proto3";

package aigc_soulmate;

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-soulmate";

service AigcSoulmate {
  // 角色
  rpc CreateAIRole(CreateAIRoleReq) returns(CreateAIRoleResp) {}
  rpc UpdateAIRole(UpdateAIRoleReq) returns(UpdateAIRoleResp) {}
  rpc DeleteAIRole(DeleteAIRoleReq) returns(DeleteAIRoleResp) {}
  rpc GetAIRole(GetAIRoleReq) returns(GetAIRoleResp) {}
  rpc GetAIRoleList(GetAIRoleListReq) returns(GetAIRoleListResp) {}
  rpc GetUserAIRoleList(GetUserAIRoleListReq) returns(GetUserAIRoleListResp) {}
  rpc GetOfficialAIRoleList(GetOfficialAIRoleListReq) returns(GetOfficialAIRoleListResp) {}
  rpc GetLatestUpdatedAIRoleList(GetLatestUpdatedAIRoleListReq) returns(GetLatestUpdatedAIRoleListResp) {}
  rpc SearchAIRole(SearchAIRoleReq) returns(SearchAIRoleResp) {}
  rpc BatchCreateRole(BatchCreateRoleReq) returns(BatchCreateRoleResp) {}
  rpc BatchUpdateRole(BatchUpdateRoleReq) returns (BatchUpdateRoleResp) {}
  rpc GetUserAIRoleListWithAppoint(GetUserAIRoleListWithAppointReq) returns(GetUserAIRoleListWithAppointResp) {}

  // 角色banner
  rpc GetBannerRoleList(GetBannerRoleListReq) returns (GetBannerRoleListResp) {}
  rpc SetBannerRoleList(SetBannerRoleListReq) returns (SetBannerRoleListResp) {}

  // 伴侣
  rpc TryCreateAIPartner(CreateAIPartnerReq) returns(CreateAIPartnerResp) {}
  rpc CreateAIPartner(CreateAIPartnerReq) returns(CreateAIPartnerResp) {}
  rpc UpdateAIPartner(UpdateAIPartnerReq) returns(UpdateAIPartnerResp) {}
  rpc DeleteAIPartner(DeleteAIPartnerReq) returns(DeleteAIPartnerResp) {}
  rpc ChangeAIPartnerRole(ChangeAIPartnerRoleReq) returns(ChangeAIPartnerRoleResp) {}
  rpc UpdateAIPartnerChatState(UpdateAIPartnerChatStateReq) returns(UpdateAIPartnerChatStateResp) {}
  rpc GetAIPartner(GetAIPartnerReq) returns(GetAIPartnerResp) {}
  rpc GetUserAIPartner(GetUserAIPartnerReq) returns(GetUserAIPartnerResp) {}
  rpc GetUserAIPartnerList(GetUserAIPartnerListReq) returns(GetUserAIPartnerListResp) {}

  // 伴侣消息
  rpc PullAIMessage(PullAIMessageReq) returns(PullAIMessageResp) {}
  rpc WriteAIMessage(WriteAIMessageReq) returns(WriteAIMessageResp) {}
  rpc ClearAIMessage(ClearAIMessageReq) returns(ClearAIMessageResp) {}

  // 角色分类
  rpc UpsertAIRoleCategory(UpsertAIRoleCategoryReq) returns(UpsertAIRoleCategoryResp) {}
  rpc DeleteAIRoleCategory(DeleteAIRoleCategoryReq) returns(DeleteAIRoleCategoryResp) {}
  rpc BatchGetAIRoleCategory(BatchGetAIRoleCategoryReq) returns(BatchGetAIRoleCategoryResp) {}
  rpc ResortAIRoleCategory(ResortAIRoleCategoryReq) returns(ResortAIRoleCategoryResp) {}

  // 角色分享
  rpc ShareRole(ShareRoleReq) returns(ShareRoleResp) {}
  rpc GetSharedRole(GetSharedRoleReq) returns(GetSharedRoleResp) {}
  rpc AllocShareIdentifier(AllocShareIdentifierReq) returns(AllocShareIdentifierResp) {}
  rpc VerifyShareIdentifier(VerifyShareIdentifierReq) returns(VerifyShareIdentifierResp) {}

  //角色管理运营后台
  rpc SearchUserRole(SearchUserRoleReq) returns(SearchUserRoleResp) {}
  rpc BatchUpdateUserRole(BatchUpdateUserRoleReq) returns(BatchUpdateUserRoleResp) {}
  rpc BatchDeleteUserRole(BatchDeleteUserRoleReq) returns(BatchDeleteUserRoleResp) {}

  // 手动触发写入审核结果
  rpc UpdateAIRoleAuditResult(UpdateAIRoleAuditResultReq) returns(UpdateAIRoleAuditResultResp) {}

  // 角色点赞
  rpc LikeAIRole(LikeAIRoleReq) returns(LikeAIRoleResp) {}
  rpc UnlikeAIRole(UnlikeAIRoleReq) returns(UnlikeAIRoleResp) {}
  rpc GetUserAIRoleLikes(GetUserAIRoleLikesReq) returns(GetUserAIRoleLikesResp) {}

  // 互动玩法
  rpc CreateInteractiveGame(CreateInteractiveGameReq) returns(CreateInteractiveGameResp);
  rpc UpdateInteractiveGame(UpdateInteractiveGameReq) returns(UpdateInteractiveGameResp);
  rpc GetInteractiveGame(GetInteractiveGameReq) returns(GetInteractiveGameResp);
  rpc SearchInteractiveGame(SearchInteractiveGameReq) returns(SearchInteractiveGameResp);
  rpc GetUserInteractiveGameList(GetUserInteractiveGameListReq) returns(GetUserInteractiveGameListResp);
  rpc BatchDeleteInteractiveGame(BatchDeleteInteractiveGameReq) returns(BatchDeleteInteractiveGameResp);
  rpc BatchUpdateInteractiveGame(BatchUpdateInteractiveGameReq) returns(BatchUpdateInteractiveGameResp);

  // 聊天模版
  rpc CreateAIChatTemplate(CreateAIChatTemplateReq) returns (CreateAIChatTemplateResp) {}
  rpc UpdateAIChatTemplate(UpdateAIChatTemplateReq) returns (UpdateAIChatTemplateResp) {}
  rpc BatchDeleteAIChatTemplate(BatchDeleteAIChatTemplateReq) returns (BatchDeleteAIChatTemplateResp) {}
  rpc GetAIChatTemplateList(GetAIChatTemplateListReq) returns (GetAIChatTemplateListResp) {}
  rpc GetBindChatTemplates(GetBindChatTemplatesReq) returns (GetBindChatTemplatesResp) {}

  // 是否读心开启
    rpc GetReadHeartEntrance(GetReadHeartEntranceRequest) returns(GetReadHeartEntranceResponse) {}
    rpc GetUserReadHeartCnt(GetUserReadHeartCntRequest) returns(GetUserReadHeartCntResponse) {}
    rpc AddReadHeartText(AddReadHeartTextRequest) returns(AddReadHeartTextResponse) {}
    rpc UpdateEntranceBanRoleId(UpdateEntranceBanRoleIdRequest) returns(UpdateEntranceBanRoleIdResponse) {}
    rpc GetEntranceBanRoleId(GetEntranceBanRoleIdRequest) returns(GetEntranceBanRoleIdResponse) {}
    rpc BatchGetReadHeartInfo(BatchGetReadHeartInfoRequest) returns(BatchGetReadHeartInfoResponse) {}

  rpc BatchAddRoleUser(BatchAddRoleUserRequest) returns(BatchAddRoleUserResponse);
  rpc BatchDelRoleUser(BatchDelRoleUserRequest) returns(BatchDelRoleUserResponse);
  rpc GetRoleUidList(GetRoleUidListRequest) returns(GetRoleUidListResponse);
  rpc BatchGetRoleUser(BatchGetRoleUserRequest) returns(BatchGetRoleUserResponse);
  rpc GetUserExclusiveRoleList(GetUserExclusiveRoleListRequest) returns(GetUserExclusiveRoleListResponse);
}

// 审核结果
enum AuditResult {
  // 机审无法识别
  AuditResultReview = 0;
  // 通过
  AuditResultPass = 1;
  // 不通过
  AuditResultReject = 2;
}

// AI角色类型
enum AIRoleType {
  // 树洞
  AIRoleTypePartner = 0;
  // 游戏/角色扮演
  AIRoleTypeGame = 1;
  // 桌宠
  AIRoleTypePet = 2;
  // 群聊角色
  AIRoleTypeGroup = 3;
}

// AI角色状态
enum AIRoleState {
  AIRoleStateNone = 0;
  // 公开
  AIRoleStatePublic = 1;
  // 私有
  AIRoleStatePrivate = 2;
}

enum AIPartnerSource {
  // 用户创建
  AIPartnerSourceUser = 0;
  // 去形象化创建
  AIPartnerSourceDeRole = 1;
  // 游戏形象
  AIPartnerSourceGame = 2;
  // 多角色
  AIPartnerSourceMultiRole = 3;
  // AIGC主动触发
  AIPartnerSourceAIGCTrigger = 4;
  // 桌宠
  AIPartnerSourcePet = 5;
}

// 角色来源
enum AIRoleSource {
  AIRoleSourceUnknown = 0;
  // 官方
  AIRoleSourceOfficial = 1;
  // 用户
  AIRoleSourceUser = 2;
}

enum GetCategorySource {
  GET_CATEGORY_SOURCE_UNKNOWN = 0;
  GET_CATEGORY_SOURCE_BACK = 1; //运营后台
  GET_CATEGORY_SOURCE_H5_HOME = 2; //h5首页
  GET_CATEGORY_SOURCE_H5_CREATE = 3; //h5创建角色页
}

enum InteractiveGameState {
  InteractiveGameStateUnspecified = 0;
  // 公开
  InteractiveGameStatePublic = 1;
  // 私有
  InteractiveGameStatePrivate = 2;
}

// 互动玩法来源
enum InteractiveGameSource {
  InteractiveGameSourceUnspecified = 0;
  // 官方
  InteractiveGameSourceOfficial = 1;
  // 用户
  InteractiveGameSourceUser = 2;
}

// 角色故事模式
enum AIRoleStoryMode {
  // 默认
  AIRoleStoryModeDefault = 0;
  // 纯故事
  AIRoleStoryModeOnlyStory = 1;
}

// 互动游戏外显
enum InteractiveGameExposure {
  InteractiveGameExposureUnspecified = 0;
  // 外显
  InteractiveGameExposureExposed = 1;
  // 隐藏
  InteractiveGameExposureUnexposed = 2;
}

// 角色使用范围
enum AIRoleScope {
  AI_ROLE_SCOPE_UNSPECIFIED = 0;
  // 用户专属
  AI_ROLE_SCOPE_USER_EXCLUSIVE = 1;
}

// AI角色分类标签信息
message AIRoleCategory {
  // 控制属性、标签展示在哪些场景
  enum Scene {
    SceneUnspecified = 0;
    // 快筛
    SceneHead = 1;
    // 弹窗面板
    ScenePanel = 2;
  }

  // 属性类型
  enum PropType {
    PropTypeUnspecified = 0;
    // 性别
    PropTypeGender = 1;
  }

  // 属性中的标签
  message Label {
    string id = 1;
    string name = 2;

    // 标签可以在哪些场景显示
    repeated Scene scenes = 3;
  }

  // 分类中的属性
  message Prop {
    string id = 1;
    string name = 2;
    PropType type = 3;

    repeated Label labels = 4;
    uint32 label_select_limit = 5;
  }

  string id = 1; //标签id
  string title = 2; //标签名称
  uint32 sort = 3; // 排序权重
  int64 update_time = 4; // 更新时间
  string tips = 5; //备注

  repeated Scene prop_scenes = 6;
  repeated Prop props = 7;
}

// AI角色进入触发文案
message AIRoleGreeting {
  // 第n次
  uint32 seq = 1;
  // 第n次进入触发文案
  string text = 2;
}

// AI角色开场白
message AIRolePrologue {
  // 开场白文本内容
  string text = 1;
  // 开场白语音链接
  string audio = 2;
}

message AIRole {
  enum LikeState {
    LikeStateUnspecified = 0;
    LikeStateLiked = 1;
    LikeStateUnliked = 2;
  }

  uint32 id = 1;
  // AI头像
  string avatar = 2;
  // AI风格
  string style = 3;
  // AI性别 0:女 1:男
  int32 sex = 4;
  // AI头像大图
  string image = 5;
  // 介绍文案
  string intro = 6;
  // AI形象类型
  AIRoleType type = 7;
  // 对话框颜色
  string dialog_color = 8;

  // 名称
  string name = 12;
  // 状态
  AIRoleState state = 13;
  // 设定/说明
  string character = 14;
  // 角色所属分类
  AIRoleCategory category = 15;
  // 标签
  repeated string tags = 16;
  // 开场白
  string prologue = 17;
  // 开场白语音 url
  string prologue_audio = 18;
  // 音色
  string timbre = 19;
  // 模型
  string prompt_id = 20;
  // 模型版本
  string prompt_version = 21;
  // 是否开启推荐回复
  bool enable_rcmd_reply = 22;
  // 角标 url
  string corner_icon = 23;
  // 第n次进入触发文案
  repeated AIRoleGreeting greetings = 24;
  // 创建者uid
  uint32 uid = 25;
  // 审核结果
  AuditResult audit_result = 26;
  // 强插位置
  uint32 insert_pos = 27;
  // 是否在首页展示/曝光 true:展示 false:不展示
  bool exposed = 28;
  // 最近一次的送审时间
  int64 audit_at = 29;
  // 配置点赞数
  int32 config_like_num = 30;
  // 用户点赞数
  uint32 user_like_num = 31;
  // 角色创建时间
  int64 created_at = 32;
  // 点赞状态
  LikeState like_state = 33;
  // 绑定的故事ID
  string story_id = 34;
  // 入口标签
  string entrance_tag = 35;
  // 领取页开场白
  AIRolePrologue collect_prologue = 36;
  // 故事模式
  AIRoleStoryMode story_mode = 37;
  // 群聊角色配置
  GroupRoleConfig group_role_config = 38;

  uint32 creator_info_type = 39; // 创作者信息展示类型,见CreatorInfoType
  uint32 appoint_uid = 40; // 官方角色指定创作者uid
  string user_role_setting = 41; // 用户角色设置

  repeated uint32 relation_ids = 42; // 关联角色ID列表
  // 更新时间
  int64 updated_at = 43;

  AIRoleScope scope = 44;
}
// 创作者信息展示类型
enum CreatorInfoType {
  CREATOR_INFO_TYPE_ANONYMOUS = 0; // 匿名
  CREATOR_INFO_TYPE_PUBLIC = 1; // 公开
  CREATOR_INFO_TYPE_HIDE = 2; // 隐藏，不展示
}

message AIPartner {
  uint32 id = 1;
  // ta的名字
  string name = 2;
  // 你希望ta怎么称呼你
  string call_name = 3;
  // AI伴侣形象卡
  AIRole role = 4;
  // [不再接收ta的消息]开关 true:打开 false:关闭
  bool silent = 5;
  // AI伴侣所属用户id
  uint32 uid = 6;
  // AI伴侣修改形象次数
  uint32 change_role_cnt = 7;
  // AI伴侣来源
  AIPartnerSource source = 8;
  // 未设置过名字
  bool unset_name = 9;
  // 未设置过角色
  bool unset_role = 10;
}

message SharedRole {
  uint32 id = 1;
  string name = 2;
  string image = 3;
  string avatar = 4;
  string character = 5;
  repeated string tags = 6;
  string prologue = 7;
  string prologue_audio = 8;
}

message AIRoleLikeInfo {
  enum LikeState {
    LikeStateUnspecified = 0;
    LikeStateLiked = 1;
    LikeStateUnliked = 2;
  }

  uint32 id = 1;
  uint32 uid = 2;
  LikeState state = 3;
}

// 互动玩法
message InteractiveGame {
  string id = 1;
  // 创建来源
  InteractiveGameSource source = 2;
  // 创建者uid
  uint32 uid = 3;
  // 绑定角色ID
  uint32 role_id = 4;
  // 绑定主题ID
  uint32 topic_id = 5;
  // 标题
  string title = 6;
  // 描述
  string desc = 7;
  // 开场白
  string prologue = 8;
  // 公开/私有状态
  InteractiveGameState state = 9;
  // 是否外显
  InteractiveGameExposure exposure = 10;
  // 创建时间
  int64 created_at = 11;
  // 更新时间
  int64 updated_at = 12;
}

message CreateAIRoleReq {
  message Role {
    uint32 id = 1;
    // 角色类型
    AIRoleType type = 2;
    // 来源
    AIRoleSource source = 3;
    // 性别 0:女 1:男 2:其他
    int32 sex = 4;
    // 名称
    string name = 5;
    // 头像
    string avatar = 6;
    // 背景图
    string image = 7;
    // 状态
    AIRoleState state = 8;
    // 设定/说明
    string character = 9;
    // 角色所属分类ID
    string category_id = 10;
    // 标签
    repeated string tags = 11;
    // 开场白
    string prologue = 12;
    // 开场白语音 url
    string prologue_audio = 13;
    // 音色
    string timbre = 14;
    // 模型ID
    string prompt_id = 15;
    // 模型版本
    string prompt_version = 16;
    // 角标 url
    string corner_icon = 17;
    // 是否开启推荐回复
    bool enable_rcmd_reply = 18;
    // 第n次进入触发文案
    repeated AIRoleGreeting greetings = 19;
    // 强插位置
    uint32 insert_pos = 21;
    // 是否展示/曝光到首页 true:展示 false:不展示
    bool exposed = 22;
    // 配置点赞数
    int32 config_like_num = 23;
    // 绑定的故事ID
    string story_id = 24;
    // 入口标签
    string entrance_tag = 25;
    // 领取页开场白
    AIRolePrologue collect_prologue = 26;
    // 故事模式
    AIRoleStoryMode story_mode = 27;
    // 群聊角色配置
    GroupRoleConfig group_role_config = 28;
    uint32 creator_info_type = 29; // 创作者信息展示类型,见CreatorInfoType
    uint32 appoint_uid = 30; // 官方角色指定创作者uid
    string user_role_setting = 31; // 用户角色设置
    // 关联角色ID列表
    repeated uint32 relation_ids = 32;
    // 使用/可见范围
    AIRoleScope scope = 33;
  }

  Role role = 1;
}

// 群开场白
message AIGroupPrologue {
  // 开场白文本内容
  string text = 1;
  // 开场白语音链接
  string audio = 2;
  // 开场白顺序
  uint32 priority = 3;
}

// 群聊角色属性配置
message GroupRoleConfig {
  // 群聊开场白
  repeated AIGroupPrologue prologues = 1;
  // 群聊角色描述
  string chat_character = 2;
  // 关系描述
  string relation_character = 3;
  // 角色描述
  string desc = 4;
  // 欢迎语
  repeated AIGroupPrologue welcome_prologues = 5;
}

message CreateAIRoleResp {
  AIRole role = 1;
}

message UpdateAIRoleReq {
  message Role {
    uint32 id = 1;
    // 性别 0:女 1:男 2:其他
    int32 sex = 2;
    // 名称
    string name = 3;
    // 头像
    string avatar = 4;
    // 背景图
    string image = 5;
    // 状态
    AIRoleState state = 6;
    // 设定/说明
    string character = 7;
    // 角色所属分类ID
    string category_id = 8;
    // 标签
    repeated string tags = 9;
    // 开场白
    string prologue = 10;
    // 开场白语音 url
    string prologue_audio = 11;
    // 音色
    string timbre = 12;
    // 模型ID
    string prompt_id = 13;
    // 模型版本
    string prompt_version = 14;
    // 角标 url
    string corner_icon = 15;
    // 是否开启推荐回复
    bool enable_rcmd_reply = 16;
    // 第n次进入触发文案
    repeated AIRoleGreeting greetings = 17;
    // 强插位置
    uint32 insert_pos = 18;
    // 是否展示/曝光到首页 true:展示 false:不展示
    bool exposed = 19;
    // 配置点赞数
    int32 config_like_num = 20;
    // 绑定的故事ID
    string story_id = 21;
    // 入口标签
    string entrance_tag = 22;
    // 领取页开场白
    AIRolePrologue collect_prologue = 23;
    // 故事模式
    AIRoleStoryMode story_mode = 24;
    // 群聊角色配置
    GroupRoleConfig group_role_config = 25;
    uint32 creator_info_type = 26; // 创作者信息展示类型,见CreatorInfoType
    uint32 appoint_uid = 27; // 官方角色指定创作者uid
    string user_role_setting = 28; // 用户角色设置
    // 关联角色ID列表
    repeated uint32 relation_ids = 29;
    // 使用/可见范围
    AIRoleScope scope = 30;
  }

  Role role = 1;
}

message UpdateAIRoleResp {
  AIRole role = 1;
}

message DeleteAIRoleReq {
  uint32 id = 1;
}

message DeleteAIRoleResp {
}

message GetAIRoleReq {
  uint32 id = 1;
}

message GetAIRoleResp {
  AIRole role = 1;
}

message GetAIRoleListReq {
  repeated uint32 role_id_list = 2;
}

message GetAIRoleListResp {
  repeated AIRole role_list = 1;
}

message GetUserAIRoleListReq {
  uint32 uid = 1;
}

message GetUserAIRoleListResp {
  repeated AIRole role_list = 1;
}

message GetUserAIRoleListWithAppointReq {
  uint32 uid = 1;
  AIRoleType role_type = 2;
  uint32 limit = 3;
}

message GetUserAIRoleListWithAppointResp {
  repeated AIRole role_list = 1;
}

message GetOfficialAIRoleListReq {
  string category_id = 1;
  AIRoleType role_type = 2;
}

message GetOfficialAIRoleListResp {
  repeated AIRole role_list = 1;
}

message GetBannerRoleListReq{
}

message GetBannerRoleListResp{
  repeated AIRole banner_role_list = 1;
}

message SetBannerRoleListReq {
  repeated uint32 role_ids = 1;
}

message SetBannerRoleListResp {
}

message CreateAIPartnerReq {
  message Partner {
    uint32 id = 1;
    // ta的名字
    string name = 2;
    // 你希望ta怎么称呼你
    string call_name = 3;
    // AI伴侣绑定的AI角色
    uint32 role_id = 4;
    // AI伴侣来源
    AIPartnerSource source = 6;
  }

  Partner partner = 1;
}

message CreateAIPartnerResp {
  AIPartner partner = 1;
}

message UpdateAIPartnerReq {
  message Partner {
    uint32 id = 1;
    // ta的名字
    string name = 2;
    // 你希望ta怎么称呼你
    string call_name = 3;
  }

  Partner partner = 1;
}

message UpdateAIPartnerResp {
  AIPartner partner = 1;
}

message DeleteAIPartnerReq {
  uint32 id = 1;
}

message DeleteAIPartnerResp {
}

message ChangeAIPartnerRoleReq {
  uint32 id = 1;
  uint32 role_id = 2;
}

message ChangeAIPartnerRoleResp {
  AIPartner partner = 1;
}

message UpdateAIPartnerChatStateReq {
  uint32 id = 1;
  bool silent = 2;
}

message UpdateAIPartnerChatStateResp {
}

message GetAIPartnerReq {
  uint32 id = 1;
}

message GetAIPartnerResp {
  AIPartner partner = 1;
}

message GetUserAIPartnerReq {
  uint32 uid = 1;

  // role_type和role_id传一个即可
  AIRoleType role_type = 2;
  uint32 role_id = 3;
}

message GetUserAIPartnerResp {
  AIPartner partner = 1;
}

message GetUserAIPartnerListReq {
  uint32 uid = 1;
  repeated AIRoleType role_types = 2;
}

message GetUserAIPartnerListResp {
  repeated AIPartner partner_list = 1;
}

message UpsertAIRoleCategoryReq {
  enum Op {
    OpUnspecified = 0;
    // 创建
    OpCreate = 1;
    // 更新
    OpUpdate = 2;
  }

  message Label {
    Op op = 1;

    string id = 2;
    string name = 3;

    repeated AIRoleCategory.Scene scenes = 4;
  }

  message Prop {
    Op op = 1;

    string id = 2;
    string name = 3;
    // 属性类型
    AIRoleCategory.PropType type = 4;
    // 属性下的标签
    repeated Label labels = 5;
    // 标签选择上限数量
    uint32 label_select_limit = 6;
  }

  message Category {
    string id = 1;
    string title = 2;
    string tips = 3;
    repeated AIRoleCategory.Scene prop_scenes = 4;
    repeated Prop props = 5;
  }

  Op op = 1;

  Category category = 2;
}

message UpsertAIRoleCategoryResp {

}

message DeleteAIRoleCategoryReq {
  string category_id = 1;
}

message DeleteAIRoleCategoryResp {

}

message BatchGetAIRoleCategoryReq {
  repeated string category_ids = 1;
  GetCategorySource source = 2;
}

message BatchGetAIRoleCategoryResp {
  repeated AIRoleCategory category_infos = 1;
}

message ResortAIRoleCategoryReq {
  repeated string ids = 1;
}

message ResortAIRoleCategoryResp {
}

message ShareRoleReq {
  uint32 role_id = 1;
}

message ShareRoleResp {
  string key = 1;
  int64 expire_at = 2;
}

message GetSharedRoleReq {
  string key = 1;
}

message GetSharedRoleResp {
  SharedRole role = 1;
}

message AllocShareIdentifierReq {
  string key = 1;
}

message AllocShareIdentifierResp {
  string identifier = 1;
}

message VerifyShareIdentifierReq {
  string key = 1;
  string identifier = 2;
}

message VerifyShareIdentifierResp {
  uint32 uid = 1;
  uint32 role_id = 2;

  string visitor_id = 3;
}

message AIMessage {
  string id = 1;

  uint32 uid = 2;
  uint32 partner_id = 3;

  bytes bin = 4;
}

message WriteAIMessageReq {
  AIMessage msg = 1;
}

message WriteAIMessageResp {
  string msg_id = 1;
}

message PullAIMessageReq {
  uint32 uid = 1;
  uint32 partner_id = 2;

  // 拉取大于msg_id的消息
  string msg_id = 3;
  uint32 limit = 4;
}

message PullAIMessageResp {
  repeated AIMessage msg_list = 1;
}

message ClearAIMessageReq {
  uint32 uid = 1;
  uint32 partner_id = 2;

  // 删除小于等于msg_id的消息
  string msg_id = 3;
}

message ClearAIMessageResp {
}

enum SearchRoleSexEnum {
  SEARCH_ROLE_SEX_NONE = 0;   // 男女都筛
  SEARCH_ROLE_SEX_MALE = 1;   // 男
  SEARCH_ROLE_SEX_FEMALE = 2; // 女
  SEARCH_ROLE_SEX_OTHER = 3;  // 其它

}

enum SearchType {
  SEARCH_TYPE_NONE = 0;
  SEARCH_TYPE_MONGO = 1; // mongo搜索
  SEARCH_TYPE_ES = 2; // es搜索
}
// 分页查询用户角色信息，仅运营后台使用
message SearchUserRoleReq {
  // 创建时间范围
  int64 start_time = 1;
  int64 end_time = 2;
  SearchRoleSexEnum sex = 3;
  // 展示分类id
  string category_id = 4;
  // 状态
  AIRoleState state = 5;
  // 是否在首页展示
  FilterExpose filter_exposed = 10;
  // 角色id
  uint32 role_id = 11;


  // 名称，设定走es搜索
  // 名称
  string name = 6;
  // 设定/说明
  string character = 7;
  // 上次拉取的最后一个角色ID
  uint32 search_after = 8;

  // searchType
  SearchType search_type = 9; // mongo搜索 or es搜索
}

enum FilterExpose {
  FILTER_EXPOSE_NONE = 0;
  FILTER_EXPOSE_TRUE = 1;
  FILTER_EXPOSE_FALSE = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SearchUserRoleResp {
  repeated AIRole user_role_list = 1;
  uint32 lastId = 2; // 下一页标识符
  bool load_finish = 3; // 是否还有下一页
}

message BatchUpdateUserRoleReq {
  message UpdateRole {
    uint32 role_id = 1;
    // 强插位置
    uint32 insert_pos = 2;
    // 是否展示/曝光到首页 true:展示 false:不展示
    bool exposed = 3;
    // 展示分类id
    string category_id = 4;
    // 配置点赞数
    int32 config_like_num = 5;
  }
  repeated UpdateRole role_list = 1;
}

message BatchUpdateUserRoleResp {
}

message BatchDeleteUserRoleReq {
  repeated uint32 role_id_list = 1;
}

message BatchDeleteUserRoleResp {
}

message UpdateAIRoleAuditResultReq {
  uint32 id = 1;
  int64 audit_at = 2;
  AuditResult result = 3;
}

message UpdateAIRoleAuditResultResp {
}

message LikeAIRoleReq {
  uint32 role_id = 1;
}

message LikeAIRoleResp {
}

message UnlikeAIRoleReq {
  uint32 role_id = 1;
}

message UnlikeAIRoleResp {
}

message GetUserAIRoleLikesReq {
  uint32 uid = 1;
  repeated uint32 role_id_list = 2;
}

message GetUserAIRoleLikesResp {
  repeated AIRoleLikeInfo like_list = 1;
}

message GetLatestUpdatedAIRoleListReq {
  // 大于updated_at
  int64 updated_at = 1;
  // 大于id
  uint32 id = 2;
  // 数量
  uint32 limit = 3;
}

message GetLatestUpdatedAIRoleListResp {
  repeated AIRole list = 1;
  // 下一页请求的id参数，0表示没有下一页了
  uint32 last_id = 2;
}

message SearchAIRoleReq {
  string content = 1;
  string last_id = 2;
  uint32 limit = 3;
}

message SearchAIRoleResp {
  repeated AIRole role_list = 1;
  string last_id = 2;
  bool load_finish = 3;  //返回true表示没有下一页了
}

message CreateInteractiveGameReq {
  message InteractiveGame {
    InteractiveGameSource source = 1;
    uint32 role_id = 2;
    uint32 topic_id = 3;
    string title = 4;
    string desc = 5;
    string prologue = 6;
    InteractiveGameState state = 7;
    InteractiveGameExposure exposure = 8;
  }

  InteractiveGame game = 1;
}

message CreateInteractiveGameResp {
  string id = 1;
}

message UpdateInteractiveGameReq {
  message InteractiveGame {
    string id = 1;
    uint32 topic_id = 2;
    string title = 3;
    string desc = 4;
    string prologue = 5;
    InteractiveGameState state = 6;
    InteractiveGameExposure exposure = 7;
  }

  InteractiveGame game = 1;
}

message UpdateInteractiveGameResp {
}

message BatchDeleteInteractiveGameReq {
  repeated string id_list = 1;
}

message BatchDeleteInteractiveGameResp {
}

message SearchInteractiveGameReq {
  // 分页游标
  string cursor = 1;
  // 每页数量，上限500
  uint32 limit = 2;

  // 搜索条件
  uint32 uid = 3;
  uint32 role_id = 4;
  uint32 topic_id = 5;
  string title = 6;
  string desc = 7;
  InteractiveGameExposure exposure = 8;
  InteractiveGameState state = 9;
  repeated int64 created_at_range = 10;
}

message SearchInteractiveGameResp {
  repeated InteractiveGame list = 1;
  // 用于下一页请求，最后一页为空
  string cursor = 2;
}

message BatchUpdateInteractiveGameReq {
  message InteractiveGame {
    string id = 1;
    InteractiveGameExposure exposure = 3;
  }

  repeated InteractiveGame games = 1;
}

message BatchUpdateInteractiveGameResp {
}

message GetUserInteractiveGameListReq {
  uint32 uid = 1;
}

message GetUserInteractiveGameListResp {
  repeated InteractiveGame list = 1;
}

message GetInteractiveGameReq {
  string id = 1;
}

message GetInteractiveGameResp {
  InteractiveGame game = 1;
}

message BatchCreateRoleReq {
  repeated CreateAIRoleReq.Role role_list = 1;
}

message BatchCreateRoleResp {
  repeated uint32 role_ids = 1;
}

message BatchUpdateRoleReq {
  repeated UpdateAIRoleReq.Role role_list = 1;
}

message BatchUpdateRoleResp {
}

message AIChatTemplate {
  BaseChatTemplate base_template = 1; // 模版信息
  repeated BindEntity bind_entities = 2; // 模版关联对象列表
}

message BindEntity {
  enum EntityType {
    EntityType_Unspecified = 0;
    EntityType_Role = 1; // 角色
  }
  EntityType entity_type = 1; // 对象类型
  uint32 id = 2; // 对象id
}

message BaseChatTemplate {
  string id = 1;
  string name = 2; // 模版名称
  repeated TemplateMsg msgs = 3; // 模版消息内容
}

message TemplateMsg {
  enum SenderType {
    SenderType_Unspecified = 0;
    SenderType_AI = 1; // AI发送
    SenderType_User = 2; // 用户发送
  }
  string content = 1; // 消息内容
  SenderType sender_type = 2;
}

message CreateAIChatTemplateReq {
  AIChatTemplate chat_template = 1;
}

message CreateAIChatTemplateResp {
}

message UpdateAIChatTemplateReq {
  AIChatTemplate chat_template = 1;
}

message UpdateAIChatTemplateResp {
}

message BatchDeleteAIChatTemplateReq {
  repeated string id_list = 1;
}

message BatchDeleteAIChatTemplateResp {
}

message GetAIChatTemplateListReq {
  // 分页页码
  uint32 page = 1;
  // 分页数量，上限100
  uint32 limit = 2;
  // 是否需要总数, 拉取第一页时需要
  bool need_count = 3;
  // 根据模版id筛选
  string template_id = 4;
}

message GetAIChatTemplateListResp {
  repeated AIChatTemplate chat_templates = 1;
  uint32 total = 2;
}

message GetBindChatTemplatesReq {
  BindEntity entity = 1;
}

message GetBindChatTemplatesResp {
  repeated BaseChatTemplate chat_templates = 1;
}


message GetReadHeartEntranceRequest {
  uint32 uid = 1;
  uint32 role_id = 2;
  string msg_id = 3;
  uint32 partner_id = 4;
}

message GetReadHeartEntranceResponse {
    bool is_read_heart_entrance = 1;
}

message GetUserReadHeartCntRequest {
    uint32 uid = 1;
    uint32 role_id = 2;
}

message GetUserReadHeartCntResponse {
    uint32 common_cnt = 1;
    uint32 role_cnt = 2;
}

// 扣读心次数类型
enum DeductionHeartCntType {
  DEDUCTION_HEART_CNT_TYPE_UNSPECIFIED = 0;
  // 通用扣次数
  DEDUCTION_HEART_CNT_TYPE_COMMON = 1;
  // 角色扣次数
  DEDUCTION_HEART_CNT_TYPE_ROLE = 2;
}

message AddReadHeartTextRequest {
  uint32 uid = 1;
  string msg_id = 2;
  string text = 3;
  uint32 role_id = 4;
  uint32 deduction_heart_cnt_type = 5;  //DeductionHeartCntType
  uint32 partner_id = 6;
}

message AddReadHeartTextResponse {
    uint32 new_common_cnt = 1; //为0时没变化
    uint32 new_role_cnt = 2;
}

message UpdateEntranceBanRoleIdRequest {
  repeated uint32 role_ids = 1;
}

message UpdateEntranceBanRoleIdResponse {
}

message GetEntranceBanRoleIdRequest {
}

message GetEntranceBanRoleIdResponse {
    repeated uint32 role_ids = 1;
}

message BatchGetReadHeartInfoRequest {
  repeated string msg_ids = 1;
}

message ReadHeartInfo {
    string msg_id = 1;
    bool is_entrance = 2;
    string text = 3;
}

message BatchGetReadHeartInfoResponse {
    repeated ReadHeartInfo read_heart_infos = 1;
}

message BatchAddRoleUserRequest {
  uint32 role_id = 1;
  repeated uint32 uid_list = 2;
}

message BatchAddRoleUserResponse {
}

message BatchDelRoleUserRequest {
  uint32 role_id = 1;
  repeated uint32 uid_list = 2;
}

message BatchDelRoleUserResponse {
}

message GetRoleUidListRequest {
  uint32 role_id = 1;

  string cursor = 2;
  uint32 limit = 3;
}

message GetRoleUidListResponse {
  repeated uint32 list = 1;

  bool has_more = 2;
  string next_cursor = 3;
}

message BatchGetRoleUserRequest {
  uint32 role_id = 1;
  repeated uint32 uid_list = 2;
}

message BatchGetRoleUserResponse {
  map<uint32, bool> exists = 1;
}

message GetUserExclusiveRoleListRequest {
  uint32 uid = 1;
  
  string cursor = 2;
  uint32 limit = 3;
}

message GetUserExclusiveRoleListResponse {
  repeated AIRole list = 1;

  bool has_more = 2;
  string next_cursor = 3;
}
