syntax = "proto3";

package aigc_account_middle;

import "tt/quicksilver/aigc/aigc-account/aigc-account.proto";

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-account-middle";

service AigcAccountMiddle {
  // 创建AI账号
  rpc CreateAIAccount(CreateAIAccountRequest) returns (CreateAIAccountResponse) {}
  // 更新AI账号
  rpc UpdateAIAccount(UpdateAIAccountRequest) returns (UpdateAIAccountResponse) {}
  // 注销AI账号
  rpc UnregisterAIAccount(UnregisterAIAccountRequest) returns (UnregisterAIAccountResponse) {}
  // 获取AI账号列表（运营后台）
  rpc GetPageAIAccount(GetPageAIAccountRequest) returns (GetPageAIAccountResponse) {}
  // 根据uid获取AI账号
  rpc GetAIAccount(GetAIAccountRequest) returns (GetAIAccountResponse) {}
  // 批量获取AI账号
  rpc BatchGetAIAccount(BatchGetAIAccountRequest) returns (BatchGetAIAccountResponse) {}
  // ai发帖上报
  rpc AddAiPost(AddAiPostReq) returns (AddAiPostResp) {}
  // 获取帖子列表
  rpc GetAiPost(GetAiPostReq) returns (GetAiPostResp) {}
}

message AIAccountInfo {
  // ID
  uint32 uid = 1;
  // 密码, 运营后台的请求才返回
  string password = 2;
  // ip地址
  string ip = 3;
  // 提示词ID, 为空则AI不会回复用户
  uint32 prompt_id = 4;
  // 音色ID
  uint32 timbre_id = 5;
  // 关联角色ID
  uint32 role_id = 6;
  // 开场白
  string prologue = 7;
  // 创建时间
  int64 create_time = 8;
  // 更新时间
  int64 update_time = 9;

  // ttid
  string ttid = 10;
  // 绑定手机号
  string phone = 11;
  // 头像
  string avatar = 12;
  // 昵称
  string nickname = 13;
  // 性别 0:女 1:男 
  uint32 sex = 14;
  // 生日, 格式: 2006-01-02
  string birthday = 15;
  // 个性签名
  string signature = 16;
  // 相册
  repeated string photo_img_urls = 17;
  // 账号经验等级
  uint32 exp_level = 18;
  // ip归属地省份
  string ip_location = 19;
  // 标识
  string identity = 20;
  // 说明
  string desc = 21;
  //排序
  uint32 sort = 22;
  //账号标签
  repeated string account_tags = 23;
  // 是否展示在扩列墙
  bool is_show_in_chat_card_wall = 24;
}

message CreateAIAccountRequest {
  message Account {
    // 绑定手机号
    string phone = 1;
    // 密码
    string password = 2;
    // ip地址
    string ip = 3;
    // 提示词ID
    uint32 prompt_id = 4;
    // 音色ID
    uint32 timbre_id = 5;
    // 关联角色ID
    uint32 role_id = 6;
    // 开场白
    string prologue = 7;
    // 头像obs链接 字节流数据太大无法直接传
    string avatar = 8;
    // 昵称
    string nickname = 9;
    // 性别 0:女 1:男
    uint32 sex = 10;
    // 生日, 格式: 2006-01-02
    string birthday = 11;
    // 个性签名
    string signature = 12;
    // 相册
    repeated string photo_img_keys = 13;
    // 发放经验值
    uint32 add_exp = 14;
    // 标识
    string identity = 15;
    // 说明
    string desc = 16;
    //排序
    uint32 sort = 17;
    //账号标签
    repeated string account_tags = 18;
    // 是否展示在扩列墙
    bool is_show_in_chat_card_wall = 19;

  }

  Account account = 1;
}

message CreateAIAccountResponse {
  uint32 id = 1;
}

// 增量更新接口，通过update_flags字段来标记哪些字段需要更新
message UpdateAIAccountRequest {
  message Account {
    // uid
    uint32 uid = 1;
    // 密码
    string password = 2;
    // ip地址
    string ip = 3;
    // 提示词ID
    uint32 prompt_id = 4;
    // 音色ID
    uint32 timbre_id = 5;
    // 关联角色ID
    uint32 role_id = 6;
    // 开场白
    string prologue = 7;
    // 头像obs链接 字节流数据太大无法直接传
    string avatar = 8;
    // 昵称
    string nickname = 9;
    // 性别 0:女 1:男, 账号性别只可以修改一次
    uint32 sex = 10;
    // 生日, 格式: 2006-01-02
    string birthday = 11;
    // 个性签名
    string signature = 12;
    // 相册
    repeated string photo_img_keys = 13;
    // 发放经验值
    uint32 add_exp = 14;
    // 绑定手机号
    string phone = 15;
    // 标识
    string identity = 16;
    // 说明
    string desc = 17;
    //排序
    uint32 sort = 18;
    //账号标签
    repeated string account_tags = 19;
    // 是否展示在扩列墙
    bool is_show_in_chat_card_wall = 20;
  }



  Account account = 1;
  // 用于标记哪些字段需要更新, key: 字段名(如 prompt_id), val: 是否更新
  map<string, bool> update_flags = 2;
}

message UpdateAIAccountResponse {
}

message UnregisterAIAccountRequest {
  uint32 uid = 1;
  string ttid = 2;
}

message UnregisterAIAccountResponse {
}

message GetPageAIAccountRequest {
  uint32 page = 1;
  uint32 size = 2;
  uint32 chat_card_wall_show_status = 3;  //扩列墙展示状态 0--默认  1--展示  2--不展示
}

message GetPageAIAccountResponse {
  repeated AIAccountInfo account_list = 1;
  uint32 total = 2;
}

message GetAIAccountRequest {
  uint32 uid = 1;
  aigc_account.GetAIAccountSource req_source = 2;
}

message GetAIAccountResponse {
  AIAccountInfo account = 1;
}

message BatchGetAIAccountRequest {
  repeated uint32 uid_list = 1;
}

message BatchGetAIAccountResponse {
  repeated AIAccountInfo account_list = 1;
}



message AddAiPostReq {
  uint32 uid = 1; // ai账号ID
  string post_id = 2; // 帖子ID
}

message AddAiPostResp {
}

message GetAiPostReq {
  uint32 page = 1;
  uint32 size = 2;
  uint32 uid = 3; // 用户ID
}

message GetAiPostResp {
  repeated PostInfo post_list = 1;
  uint32 total_num = 2;
}

message PostInfo {
  string post_id = 1; // 帖子ID
  string content = 2; // 帖子内容
  repeated AttachmentInfo attachments = 3; // 附件
  uint64 create_time = 4; // 创建时间
}

message AttachmentInfo {
  uint32 attachment_type = 1; // 附件类型, 引用/ugc/content.proto:135
  string attachment_content = 2; // 附件内容
  string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
  string key = 4;
  string vm_content= 5; // 带水印的
  // 只有type = VIDEO 才有的
  string param = 6;
  //原视频封面
  string origin_video_cover = 7;
}