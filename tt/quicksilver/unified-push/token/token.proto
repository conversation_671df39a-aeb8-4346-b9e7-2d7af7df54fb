syntax = "proto3";

package token;

import "google/protobuf/timestamp.proto";

option go_package="golang.52tt.com/protocol/services/unified-push/token";


// 统一离线推送token相关接口
service UnifiedPushTokenService {
  // 查询token
  rpc QueryDeviceToken(QueryDeviceTokenRequest) returns (QueryDeviceTokenResponse) {}
  // 更新token
  rpc UpdateDeviceToken(UpdateDeviceTokenRequest) returns (UpdateDeviceTokenResponse) {}
  // 删除token
  rpc RemoveDeviceToken(RemoveDeviceTokenRequest) returns (RemoveDeviceTokenResponse) {}
  // 创建user device token数据库表格，非通道管理员禁止使用该接口！
  rpc CreateAppUserTable(CreateAppUserTableRequest) returns (CreateAppUserTableResponse) {}
  // 忽略token更新事件的消息数，用于临时清理消息队列中的数据，非通道管理员禁止使用该接口！
  rpc SetIgnoreTokenEventSize(SetIgnoreTokenEventSizeRequest) returns (SetIgnoreTokenEventSizeResponse) {}
}

// 系统类型，注意与type.proto中的OsType保持一致
enum OsType {
  // 未指定
  OS_TYPE_UNSPECIFIED = 0;
  // 安卓
  OS_TYPE_ANDROID = 1;
  // ios
  OS_TYPE_IOS = 2;
  // 鸿蒙
  OS_TYPE_HARMONY = 3;
}

// 离线推送sdk供应商类型，注意与type.proto中的ProviderType保持一致
enum ProviderType {
  // 未指定
  PROVIDER_TYPE_UNSPECIFIED = 0;
  // google fcm
  PROVIDER_TYPE_FCM = 1;
  // ios apns
  PROVIDER_TYPE_APNS = 2;
  // 鸿蒙
  PROVIDER_TYPE_HARMONY = 3;
  // 个推
  PROVIDER_TYPE_GETUI = 4;
  // 华为
  PROVIDER_TYPE_HUAWEI = 5;
  // 荣耀
  PROVIDER_TYPE_HONOR = 6;
  // 小米
  PROVIDER_TYPE_XIAOMI = 7;
  // oppo
  PROVIDER_TYPE_OPPO = 8;
  // vivo
  PROVIDER_TYPE_VIVO = 9;

  // 服务器专用，走TT在线
  PROVIDER_TYPE_TT_ONLINE = 100;
}

// Token事件类型
enum TokenEventType {
    // 未指定
    TOKEN_EVENT_TYPE_UNSPECIFIED = 0;
    // 更新token
    TOKEN_EVENT_TYPE_UPDATE = 1;
    // 删除token
    TOKEN_EVENT_TYPE_REMOVE = 2;
}

// Token事件明细
message TokenEvent {
    // Token事件类型
    TokenEventType type = 1;
    // Token明细信息
    DeviceToken token = 2;
}

// 设备token明细信息
message DeviceToken {
  // appid，必填，新接入的App或者新马甲包请联系通道负责人分配新appid
  uint32 appid = 1;
  // uid，必填
  uint64 uid = 2;
  // 第三方合作商token，必填，手机厂家原生token请填在下面的manufacture_token，如果没有使用第三方合作商，则填手机厂商原生token
  bytes token = 3;
  // 离线推送sdk供应商类型，必填
  ProviderType provider_type = 4;
  // 手机厂商原生token，必填
  bytes manufacture_token = 5;
  // 设备id
  string device_id = 6;
  // 系统类型，必填，安卓、苹果、鸿蒙等
  OsType os_type = 7;
  // 系统版本
  string os_ver = 8;
  // 手机型号，必填
  string phone_model = 9;
  // 离线推送包名，必填，example: com.yiyou.ga com.yiyou.tt com.yiyou.ga.debug com.yiyou.enterprise.tt
  string bundle_id = 10;
  // 手机厂商，必填，全小写，如：huawei, honor, xiaomi, oppo, vivo, meizu, samsung, apple, ...
  string manufacture = 11;
  // 终端类型，目前只有TT有这玩意
  uint32 terminal_type = 12;
  // 客户端版本
  uint32 client_version = 13;
  // 安装包渠道
  string pkg_channel = 14;
  // 用户当前正在使用的语言, 只保留第一语言，例如: zh-CN,zh;q=0.9
  string accept_language = 15;
  // 个推用户画像标签cid
  string gt_user_cid = 16;
  // 更新时间，单位：nano second
  int64 mtime = 17;
  // 归因系统数据采集，TT线才需要用到
  string ip = 18;
  // 归因系统数据采集，TT线才需要用到
  string user_agent = 19;
  // 扩展字段
  map<string, string> extra = 20;
}

// 查询token请求
message QueryDeviceTokenRequest {
  // 指定需要查询的app列表
  repeated uint32 appid_list = 1;
  // uid列表，最多1000个，否则会返回错误
  repeated uint64 uid_list = 2;
  // true: 返回uid所有马甲包token，一般情况用不到，false: 只返回uid最后一次登录的马甲包token
  bool return_all_app_token = 3;
}

// 设备token信息列表
message DeviceTokenList {
  // 用户设备信息列表，可能包含多个马甲包的token
  repeated DeviceToken tokens = 1;
}

// 查询token应答
message QueryDeviceTokenResponse {
  // 用户设备信息列表
  map<uint64, DeviceTokenList> user_tokens = 1;
}

// 更新token请求
message UpdateDeviceTokenRequest {
  // 用户设备信息
  DeviceToken device_token = 1;
  // 发送时间
  google.protobuf.Timestamp timestamp = 2;
}

// 更新token应答
message UpdateDeviceTokenResponse {
  // nothing
}

// 删除token请求
message RemoveDeviceTokenRequest {
  // 用户设备信息
  DeviceToken device_token = 1;
  // 发送时间
  google.protobuf.Timestamp timestamp = 2;
}

// 删除token应答
message RemoveDeviceTokenResponse {
  // nothing
}

// 添加对应app的用户设备表请求
message CreateAppUserTableRequest {
  // 网关业务id
  string biz_id = 1;
}

// 添加对应app的用户设备表应答
message CreateAppUserTableResponse {
  // nothing
}

// 设置忽略token更新事件的消息数请求
message SetIgnoreTokenEventSizeRequest {
  uint32 appid = 1;
  int64 count = 2;
}

// 设置忽略token更新事件的消息数应答
message SetIgnoreTokenEventSizeResponse {
  // nothing
}


