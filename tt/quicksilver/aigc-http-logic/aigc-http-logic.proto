syntax = "proto3";

package aigc_http_logic;

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-http-logic";

message BaseRequest {
  string device_id = 1;
  uint32 market_id = 2;
  uint32 client_type = 3;
  uint32 client_version = 4;
}

// 送审结果
enum AuditResult {
  // 机审无法识别
  AuditResultReview = 0;
  // 通过
  AuditResultPass = 1;
  // 不通过
  AuditResultReject = 2;
}

enum LikeState {
  LIKE_STATE_UNSPECIFIED = 0;
  LIKE_STATE_LIKED = 1;
  LIKE_STATE_UNLIKED = 2;
}

message AIRoleCategory {
  message Label {
    string id = 1;
    string name = 2;
    repeated uint32 scenes = 3;
  }

  message Prop {
    string id = 1;
    string name = 2;
    repeated Label labels = 4;
    uint32 label_select_limit = 5;
  }

  string id = 1;
  string title = 2;
  repeated uint32 prop_scenes = 3;
  repeated Prop props = 4;
}

// 创作者信息展示类型
enum CreatorInfoType {
  CREATOR_INFO_TYPE_ANONYMOUS = 0; // 匿名
  CREATOR_INFO_TYPE_PUBLIC = 1; // 公开
  CREATOR_INFO_TYPE_HIDE = 2; // 隐藏，不展示
}

message CreatorInfo {
  uint32 uid = 1; // 创作者uid
  string name = 2; // 创作者名称
  string account = 3; // 创作者account
  bool is_follow = 4; // 是否已关注
}

message AIRole {
  message Category {
    string id = 1;
    string title = 2;
  }

  uint32 id = 1;
  uint32 type = 2;
  int32  sex = 3;
  string name = 4;
  string style = 5;
  uint32 state = 6;
  string avatar = 7;
  string image = 8;
  string intro = 9;
  string dialog_color = 10;
  string character = 11;
  string prologue = 12;
  string prologue_audio = 13;
  string timbre = 14;
  string corner_icon = 15;
  uint32 audit_result = 16;
  repeated string tags = 17;
  Category category = 18;
  // 是否开启推荐回复
  bool enable_rcmd_reply = 19;
  uint32 like_num = 20;
  uint32 like_state = 21;
  string story_id = 22;
  uint32 creator_uid = 23; // 创建者uid
  bool exposed = 24; // 首页是否可见
  // 透传从中台获取的角色扩展信息
  bytes ext = 25;
  uint32 story_mode = 26;
  uint32 creator_info_type = 27; // 创作者信息展示类型,见CreatorInfoType
  CreatorInfo creator_info = 28; // 创作者信息
  string user_role_setting = 29; // 用户角色设置
  // 使用/可见范围 see aigc-soulmate.proto enum AIRoleScope
  uint32 scope = 30;
}

message PetRole {
  uint32 id = 1;
  int32  sex = 3;
  string name = 4;
  string character = 5;
  bytes ext = 6;
  // 领取页开场白
  AIRolePrologue collect_prologue = 7;
  string corner_icon = 8;
}

// AI关系
message AIRelationship {
  uint32 id = 1;
  string name = 2;
}

// AI形象立绘
message AIPhotograph {
  uint32 id = 1;
  string avatar = 2;
  string image = 3;
}

message CustomVoice {
  // 音色ID
  string id = 1;
  // 权重 0.0~1.0
  float weight = 2;
}

message AIPartner {
  message Role {
    uint32 id = 1;
    uint32 type = 2;
    int32  sex = 3;
    string name = 4;
    uint32 state = 5;
    string avatar = 6;
    string image = 7;
    string dialog_color = 8;
    string character = 9;
    string prologue = 10;
    string prologue_audio = 11;
    string timbre = 12;
    uint32 audit_result = 13;
    bool enable_rcmd_reply = 14;
    uint32 like_num = 15;
    uint32 like_state = 16;
    string story_id = 17;
    uint32 creator_uid = 18; // 创建者uid
    bool exposed = 19; // 首页是否可见
    bytes ext = 20;
    uint32 story_mode = 21;
    uint32 creator_info_type = 22; // 创作者信息展示类型,见CreatorInfoType
    CreatorInfo creator_info = 23; // 创作者信息
    string user_role_setting = 24; // 用户角色设置
    uint32 scope = 25; // 使用/可见范围 see aigc-soulmate.proto enum AIRoleScope
  }

  uint32 id = 1;
  // ta的名字
  string name = 2;
  // 你希望ta怎么称呼你
  string call_name = 3;
  // AI伴侣形象卡
  Role role = 4;
  // [不再接收ta的消息]开关 true:打开 false:关闭
  bool silent = 5;
  // AI伴侣修改形象次数
  uint32 change_role_cnt = 7;
  // AI伴侣形象立绘
  AIPhotograph photograph = 8;
  // AI伴侣关系
  AIRelationship relation = 9;
  // AI伴侣来源
  uint32 source = 10;
  // 未设置过名字
  bool unset_name = 11;
  // 是否展示hint内容
  bool show_hint = 12;
  // hint内容
  string hint = 13;
  // 未设置角色
  bool unset_role = 14;
  // 关系角色列表
  repeated RelationRoleInfo relation_roles = 15;
}

message RelationRoleInfo {
  uint32 id = 1;
  uint32 type = 2;
  string name = 3;
  uint32 state = 4;
  string avatar = 5;
  string image = 6;
  bool exposed = 7; // 首页是否可见
  RelationType relation_type = 8; // 关系类型
}

enum RelationType {
  RELATION_TYPE_UNSPECIFIED = 0;
  RELATION_TYPE_RELATE = 1; // 手动关联
  RELATION_TYPE_SAME_CREATOR = 2; // 同作者
}

message PetPartner {
  message Role {
    uint32 id = 1;
    uint32 type = 2;
    int32 sex = 3;
    string avatar = 4;
    bytes ext = 5;
  }

  uint32 id = 1;
  // 来源
  uint32 source = 2;
  // 绑定的角色
  Role role = 3;

  // ta的名字
  string name = 4;
  // ta对你的称呼
  string call_name = 5;

  // 自定义音色
  repeated CustomVoice custom_voices = 6;
}

message ChattingPartner {
  message Role {
    uint32 id = 1;
    uint32 type = 2;
    int32  sex = 3;
    string name = 4;
    uint32 state = 5;
    string avatar = 6;
    uint32 audit_result = 13;
    uint32 creator_uid = 18; // 创建者uid
    bool exposed = 19; // 首页是否可见
  }

  uint32 id = 1;
  string name = 2;

  Role role = 3;
}

message BannerRole {
  uint32 id = 1;
  uint32 type = 2;
  int32 sex = 3;
  string name = 4;
  string image = 5;
  string character = 6;
  string corner_icon = 7;
  string prologue_audio = 8;
}

message RcmdRole {
  uint32 id = 1;
  uint32 type = 2;
  int32 sex = 3;
  string name = 4;
  string image = 5;
  string character = 6;
  string corner_icon = 7;
  string prologue_audio = 8;
  repeated string tags = 9;
  string avatar = 10;
}

message AIRolePrologue {
  string text = 1;
  string audio = 2;
  uint32 seq = 3;
}

message InteractiveGame {
  string id = 1;
  // 绑定角色ID
  uint32 role_id = 4;
  // 绑定主题ID
  uint32 topic_id = 5;
  // 标题
  string title = 6;
  // 描述
  string desc = 7;
  // 开场白
  string prologue = 8;
  // 公开/私有状态
  uint32 state = 9;
  // 绑定角色名称
  string role_name = 10;
  // 绑定角色头像
  string role_avatar = 11;
}

message GroupInfo {
  uint32 id = 1;

  uint32 owner_uid = 2;
  uint32 template_id = 3;

  // 建群来源 see aigc-group.proto enum GroupSource
  uint32 source = 4;

  uint32 like_num = 6;
  uint32 member_num = 7;
  uint32 max_real_user_num = 8; // 最大真实用户数

  uint32 like_state = 11;
  uint32 match_strategy = 12;
  uint32 leave_strategy = 13;

  int32 sex = 16;
  string name = 17;
  string desc = 18;
  string avatar = 19;
  string chat_background = 20;
  uint32 group_type = 21; // 见aigc-group.proto GroupType

  string background_music = 22; // 背景音乐

  repeated string quick_speak_texts = 32; // 一键发言列表
}

message GroupRole {
  uint32 id = 1;

  int32 sex = 6;
  string name = 7;
  string avatar = 8;
  string character = 9;

  repeated AIRolePrologue prologue_list = 16;
}

message GroupTemplateInfo {
  uint32 id = 1;

  uint32 like_num = 6;
  uint32 member_num = 7;

  uint32 like_state = 11;

  int32 sex = 16;
  string name = 17;
  string desc = 18;
  string avatar = 19;
  string chat_background = 20;
  uint32 group_type = 21; // 见aigc-group.proto GroupType

  message ScriptInfo {
    string background_music = 1; // 背景音乐
  }
  ScriptInfo script_info = 22; // 剧本玩法字段
  repeated AIRolePrologue default_prologue_list = 23; // 开场白列表

  repeated GroupRole role_list = 31;

  repeated string quick_speak_texts = 32; // 一键发言列表
}

message UserRole {
  uint32 role_id = 1;
  string role_name = 2;
  string role_avatar = 3;
  string role_character = 4;
  repeated string quick_speak_texts = 5; // 一键发言列表
}

message GroupMember {
  uint32 id = 1;
  uint32 type = 2;
  string name = 3;
  string avatar = 4;
  string account = 5;
  string character = 6;

  bool is_follow = 7; // 是否已关注
  UserRole user_role = 8; // 用户角色信息
  repeated AIRolePrologue prologue_list = 11;

  int32 sex = 12;
  int64 join_time = 13; // 加入时间,毫秒级别
  int64 last_active_time = 14; // 最后活跃时间,秒级别
}

message Entity {
  enum Type {
    TYPE_UNSPECIFIED = 0;
    TYPE_ROLE = 1;
    TYPE_GROUP_TEMPLATE = 2;
    TYPE_PARTNER = 3;
  }

  uint32 id = 1;
  Type type = 2;
}

message LevelConfig {
  // 等级
  uint32 level = 1;
  // 到达等级需要的亲密值
  uint32 value = 2;
  // 等级权益描述
  string benefit_desc = 3;
}

message LevelUpCondition {
  string id = 1;

  // 图标
  string icon = 6;
  // 标题
  string title = 7;
  // 描述
  string desc = 8;

  // 满足条件增加的亲密值
  uint32 growth_value = 16;
}

message GameTopic {
  uint32 id = 1;    // 主题ID
  string name = 2;  // 主题名
}

message GameInfo {
  string id = 1;   // 玩法ID
  string name = 2; // 玩法名
  string icon = 3;  // 图标
  string bgm = 4;  // 背景音乐URL
  string bg = 5;  // 背景图
  int32 creator = 6;  // 创建者 1:官方 2:用户
  uint32 topic_id = 7; //主题ID
  string scene_desc = 8; //场景描述
  string subtitle = 16; // 副标题
}

message OutGames {
  uint32 topic_id = 1;
  repeated GameInfo games = 2;
}

message CreateAIPartnerRequest {
  message Partner {
    // @gotags: binding:"min=0,max=5"
    uint32 source = 1;
    uint32 role_id = 2;
    string name = 3;
    string call_name = 4;
  }

  // @gotags: binding:"required"
  Partner partner = 1;
}

message CreateAIPartnerResponse {
  AuditResult name_result = 1;
  AuditResult call_name_result = 2;
}

message UpdateAIPartnerRequest {
  message Partner {
    // @gotags: binding:"required"
    uint32 id = 1;
    string name = 2;
    string call_name = 3;
    repeated CustomVoice custom_voices = 4;
  }

  // @gotags: binding:"required"
  Partner partner = 1;
}

message UpdateAIPartnerResponse {
  AuditResult name_result = 1;
  AuditResult call_name_result = 2;
}

message SwitchAIPartnerSilentRequest {
  // @gotags: binding:"required"
  uint32 id = 1;
  bool silent = 2;
}

message SwitchAIPartnerSilentResponse {
}

message RebindAIPartnerRoleRequest {
  // @gotags: binding:"required"
  uint32 id = 1;
  // @gotags: binding:"required"
  uint32 role_id = 2;
}

message RebindAIPartnerRoleResponse {
}

message ReportAIPartnerChattingRequest {
  // @gotags: binding:"required"
  uint32 partner_id = 1;
}

message ReportAIPartnerChattingResponse {
}

message DeleteAIPartnerRequest {
  // @gotags: binding:"required"
  uint32 id = 1;
}

message DeleteAIPartnerResponse {
}

message GetAIPartnerRequest {
  // @gotags: form:"role_id,default=0"
  uint32 role_id = 1;
  // @gotags: form:"role_type,default=0"
  uint32 role_type = 2;
}

message GetAIPartnerResponse {
  AIPartner partner = 1;
}

message GetAIPetListRequest {
}

message GetAIPetListResponse {
  repeated PetRole role_list = 1;
}

message GetAIRoleRequest {
  // @gotags: binding:"required"
  uint32 role_id = 1;
}

message GetAIRoleResponse {
  AIRole role = 1;
  repeated RelationRoleInfo relation_roles = 2; // 关系角色列表
}

message CreateAIRoleRequest {
  message Role {
    // @gotags: binding:"min=0,max=2"
    int32 sex = 1;
    // @gotags: binding:"required"
    string name = 2;
    // @gotags: binding:"min=1,max=2"
    uint32 state = 3;
    // @gotags: binding:"required,url"
    string avatar = 4;
    // @gotags: binding:"required,url"
    string image = 5;
    // @gotags: binding:"required"
    string character = 6;
    // @gotaags: binding:"required"
    string category_id = 7;
    // @gotags: binding:"required"
    string prologue = 8;
    string timbre = 9;
    repeated string tags = 10;
    uint32 creator_info_type = 11; // 创作者信息展示类型,见CreatorInfoType
    string user_role_setting = 12; // 用户角色设置
  }

  // @gotags: binding:"required"
  Role role = 1;
}

message CreateAIRoleResponse {
  uint32 id = 1;
}

message UpdateAIRoleRequest {
  message Role {
    // @gotags: binding:"required"
    uint32 id = 1;
    // @gotags: binding:"min=0,max=2"
    int32  sex = 2;
    // @gotags: binding:"required"
    string name = 3;
    // @gotags: binding:"min=1,max=2"
    uint32 state = 4;
    // @gotags: binding:"required,url"
    string avatar = 5;
    // @gotags: binding:"required,url"
    string image = 6;
    // @gotags: binding:"required"
    string character = 7;
    // @gotaags: binding:"required"
    string category_id = 8;
    // @gotags: binding:"required"
    string prologue = 9;
    string timbre = 10;
    repeated string tags = 11;
    uint32 creator_info_type = 12; // 创作者信息展示类型,见CreatorInfoType
    string user_role_setting = 13; // 用户角色设置
  }

  // @gotags: binding:"required"
  Role role = 1;
}

message UpdateAIRoleResponse {
}

message DeleteAIRoleRequest {
  // @gotags: binding:"required"
  uint32 id = 1;
}

message DeleteAIRoleResponse {
}


message ShareRoleRequest {
  // @gotags: binding:"required"
  uint32 role_id = 1;
}

message ShareRoleResponse {
  string key = 1;
  int64 expire_at = 2;
}

message LikeRoleRequest {
  // @gotags: binding:"required"
  uint32 id = 1;
}

message LikeRoleResponse {
  uint32 like_state = 1;
}

message GetUserAIRoleListRequest {
}

message GetUserAIRoleListResponse {
  repeated AIRole roles = 1;
}

message GetBannerAIRoleListRequest {
}

message GetBannerAIRoleListResponse {
  repeated BannerRole list = 1;
}

message GetRcmdAIRoleListRequest {
  message Prop {
    string id = 1;
    repeated string labels = 2;
  }

  // @gotags: binding:"required"
  string role_tag_id = 1;//角色分类标签
  // @gotags: binding:"required"
  uint32 get_mode = 2; //1代表下一页，2代表刷新。
  repeated uint32 no_browse_list = 3; //未曝光的角色id列表
  repeated Prop props = 4; //筛选条件
  repeated Entity top_entities = 5; // 指定置顶的角色/群模板
}

message GetRcmdAIRoleListResponse {
  repeated RcmdRole role_list = 1; //角色列表
  bool load_finish = 2; //返回true表示没有下一页了
  repeated ListMixDataItem mix_datas = 3;
}

message ListMixDataItem {
  // 1-角色 2-群聊
  uint32 type = 1;
  RcmdRole role_info = 2;
  GroupDetailInfo group_info = 3;
}

message GroupDetailInfo {
  uint32 id = 1;
  string name = 2;
  string character = 3;
  repeated string tags = 4;
  string avatar = 5;
  int32 sex = 6;
  string chat_background = 7;
  string home_background = 8;
  string group_icon = 9;
  uint32 like_num = 10;
  uint32 template_id = 11;
  uint32 owner_uid = 12;
}

message GetAIRoleCategoryListRequest {
}

message GetAIRoleCategoryListResponse {
  repeated AIRoleCategory category_infos = 1;
}

message HandleRcmdCommandRequest {
  string cmd = 1;
  bytes payload = 2;
}

message HandleRcmdCommandResponse {
  uint32 code = 1;
  string msg = 2;
  bytes data = 3;
}

message GetPetPartnerRequest {
}

message GetPetPartnerReponse {
  PetPartner partner = 1;
}

message SearchRoleRequest {
  // @gotags: form:"content"
  string content = 1;
  // @gotags: form:"last_id"
  string last_id = 2;
}

message SearchRoleResponse {
  repeated AIRole data = 1;
  string last_id = 2;
  bool load_finish = 3;  //返回true表示没有下一页了
  repeated SearchMixDataItem mix_datas = 4;
}

message SearchMixDataItem {
  // 1-角色 2-群聊
  uint32 type = 1;
  AIRole role_info = 2;
  GroupDetailInfo group_info = 3;
}

message CreateInteractiveGameRequest {
  message InteractiveGame {
    // @gotags: binding:"required"
    uint32 role_id = 1;
    // @gotags: binding:"required"
    uint32 topic_id = 2;
    // @gotags: binding:"required,max=30"
    string title = 3;
    // @gotags: binding:"required,max=300"
    string desc = 4;
    // @gotags: binding:"required,max=50"
    string prologue = 5;
    // @gotags: binding:"required,min=1,max=2"
    uint32 state = 6;
  }

  // @gotags: binding:"required"
  InteractiveGame game = 1;
}

message CreateInteractiveGameResponse {
  string id = 1;
  AuditResult title_audit_result = 2;
  AuditResult desc_audit_result = 3;
  AuditResult prologue_audit_result = 4;
}

message UpdateInteractiveGameRequest {
  message InteractiveGame {
    // @gotags: binding:"required"
    string id = 1;
    // @gotags: binding:"required"
    uint32 topic_id = 2;
    // @gotags: binding:"required,max=30"
    string title = 3;
    // @gotags: binding:"required,max=300"
    string desc = 4;
    // @gotags: binding:"required,max=50"
    string prologue = 5;
    // @gotags: binding:"required,min=1,max=2"
    uint32 state = 6;
  }

  // @gotags: binding:"required"
  InteractiveGame game = 1;
}

message UpdateInteractiveGameResponse {
  AuditResult title_audit_result = 1;
  AuditResult desc_audit_result = 2;
  AuditResult prologue_audit_result = 3;
}

message DeleteInteractiveGameRequest {
  string id = 1;
}

message DeleteInteractiveGameResponse {
}

message GetInteractiveGameListRequest {
}

message GetInteractiveGameListResponse {
  repeated InteractiveGame list = 1;
}

message GetChattingPartnerListRequest {
}

message GetChattingPartnerListResponse {
  repeated ChattingPartner list = 1;
}

message ChatTemplate {
  string id = 1;
  // 模版名称
  string name = 2;
  // 模版消息内容
  repeated TemplateMsg msgs = 3;
}

message TemplateMsg {
  enum SenderType {
    SenderType_Unspecified = 0;
    SenderType_AI = 1; // AI发送
    SenderType_User = 2; // 用户发送
  }
  // 消息内容
  string content = 1;
  // 发送者类型
  SenderType sender_type = 2;
}

message GetBindChatTemplatesReq {
  // 模版关联对象类型
  uint32 entity_type = 1;
  // 对象ID
  uint32 entity_id = 2;
}

message GetBindChatTemplatesResp {
  repeated ChatTemplate chat_templates = 1;
}


message CreateGroupRequest {
  // @gotags: binding:"required"
  uint32 template_id = 1;
}

message CreateGroupResponse {
  uint32 id = 1;
}

message DeleteGroupRequest {
  // @gotags: binding:"required"
  uint32 id = 1;
}

message DeleteGroupResponse {
}

message GetGroupInfoRequest {
  // 根据群组id获取
  // @gotags: form:"id"
  uint32 id = 1;
  // 根据群模板id
  // @gotags: form:"template_id"
  uint32 template_id = 2;
}

message GetGroupInfoResponse {
  GroupInfo info = 1;
}

message GetGroupTemplateInfoRequest {
  // @gotags: binding:"required" form:"id"
  uint32 id = 1;
}

message GetGroupTemplateInfoResponse {
  GroupTemplateInfo info = 1;
}

message GetGroupListRequest {
}

message GetGroupListResponse {
  message Group {
    uint32 id = 1;

    int32 sex = 6;
    string name = 7;
    string avatar = 8;
    uint32 group_type = 9; // 见aigc-group.proto GroupType
  }

  repeated Group list = 1;
}

message GetGroupMemberListRequest {
  // @gotags: binding:"required" form:"group_id"
  uint32 group_id = 1;
}

message GetGroupMemberListResponse {
  repeated GroupMember list = 1;
  repeated AIRolePrologue default_prologue_list = 2;
}

enum AttitudeAction {
  ATTITUDE_ACTION_UNSPECIFIED = 0;
  ATTITUDE_ACTION_LIKE = 1; // 点赞
  ATTITUDE_ACTION_UNLIKE = 2; // 取消点赞
}

enum ObjectType {
  OBJECT_TYPE_UNSPECIFIED = 0;
  OBJECT_TYPE_GROUP_TEMPLATE = 1;
}

message AttitudeRequest {
  uint32 action = 1; // 操作类型AttitudeAction
  uint32 object_id = 2; // 对象id
  uint32 object_type = 3; // 对象类型
}


message AttitudeResponse {
}

message HadAttitudeRequest {
  uint32 object_id = 1; // 对象id
  uint32 object_type = 2; // 对象类型
}

message HadAttitudeResponse {
  bool had_attitude = 1; // 是否已点赞
}

message GetReadHeartCountRequest {
  uint32 role_id = 1;
  uint32 partner_id = 2;
}

message GetReadHeartCountResponse {
  uint32 read_heart_count = 1;
}

message ReadHeartRequest {
  string msg_id = 1; // 消息id
  uint32 role_id = 2;
  uint32 partner_id = 3;
  string content = 4;
}

message ReadHeartResponse {
  uint32 read_heart_count = 1;
  string read_heart_text = 2;
}

message BatchGetMsgRequest {
  repeated string msg_ids = 1;
}

message ReadHeartInfo {
  string msg_id = 1;
  bool is_entrance = 2;
  string text = 3;
}

message BatchGetMsgResponse {
  repeated ReadHeartInfo read_heart_infos = 1;
}

message GetExtraInfoRequest {
  Entity entity = 1;
}

message ChatBackground {
  string id = 1;
  string background_url = 2; // 背景图url
  bool show_head_img = 3; // 是否展示头像
  string name = 4; // 名称
  enum State {
    STATE_UNSPECIFIED = 0;
    STATE_UNLOCK = 1; // 已解锁
    STATE_LOCK = 2; // 未解锁
    STATE_USING = 3; // 使用中
  }
  State state = 5; // 状态
  string unlock_condition = 6; //解锁条件信息
}

message GetExtraInfoResponse {
  ChatBackground cur_background = 1;
}

message GetChatBackgroundListRequest {
  Entity entity = 1;
}

message GetChatBackgroundListResponse {
  repeated ChatBackground list = 1; // 聊天背景配置列表
}

message SwitchChatBackgroundRequest {
  Entity entity = 1;
  string background_id = 2; // 背景id
}

message SwitchChatBackgroundResponse {
}

message  Relation {
  string id = 1;
  string name = 2; // 名称
  string unlock_tip = 3; // 解锁提示
  enum State {
    STATE_UNSPECIFIED = 0;
    STATE_UNLOCK = 1; // 已解锁
    STATE_LOCK = 2; // 未解锁
    STATE_USING = 3; // 使用中
  }
  State state = 4; // 状态
  // 外显icon
  string icon = 5;
  // 亲密度页面展示背景图
  string intimacy_background_img = 6;
}

message GetRelationListRequest {
  Entity entity = 1;
}

message GetRelationListResponse {
  repeated Relation list = 1;
}

message SwitchRelationRequest{
  Entity entity = 1;
  string relation_id = 2; // 关系id
}

message SwitchRelationResponse{
}

message GetIntimacyInfoRequest {
  Entity entity = 1;
}

message GetIntimacyInfoResponse {
  message Relation {
    string id = 1;
    string name = 2;
    string background = 3;
  }

  // 当前等级
  uint32 cur_level = 1;
  // 当前亲密值
  uint32 cur_value = 2;

  // 相遇的第x天
  uint32 meet_day = 6;
  // 今日增长的亲密值
  uint32 today_growth_value = 7;

  // 关系
  Relation relation = 11;

  // 等级配置
  repeated LevelConfig levels = 16;
  // 升级条件
  repeated LevelUpCondition level_up_conditions = 17;
}

message GetScriptFilterItemRequest {
}

message FilterItem {
  enum FilterType {
    FILTER_TYPE_UNSPECIFIED = 0;
    // 女生
    FILTER_TYPE_SEX_FEMALE = 1;
    // 男生
    FILTER_TYPE_SEX_MALE = 2;
    // 所有性别
    FILTER_TYPE_SEX_ALL = 3;
  }
  FilterType type = 1;
  string display_name = 2;
}

message GetScriptFilterItemResponse {
  repeated FilterItem list = 1;
}

message GetScriptListRequest {
  repeated FilterItem filter_items = 1;
  string cursor = 2;
}

message ScriptItem {
  uint32 id = 1;
  string name = 2;
  string character = 3;
  string button_display_text = 4;
  message PlayingInfo {
    repeated string accounts = 1;
    string text = 2;
    repeated string fall_back_avatar_urls = 3;
  }
  PlayingInfo playing_info = 5;
  string home_background_img = 6;
  repeated string tags = 7;
  string corner_icon = 8;
  repeated int32 suitable_sex = 9; // 适合性别 0:女 1:男 2：其他

}

message GetScriptListResponse {
  repeated ScriptItem list = 1;
  string next_cursor = 2;
}

message  GetScriptHotBannerRequest {
}

message GetScriptHotBannerResponse {
  string banner_title = 1;
  message BannerInfo {
    string id = 1;
    uint32 template_id = 2;
    string top_title = 3;
    string sub_title = 4;
    string background_img = 5;
    string button_display_text = 6;
    string button_color = 7;
    string bar_color = 8;
    string icon = 9;
    repeated int32 suitable_sex = 10; // 适合性别 0:女 1:男 2：其他
  }
  repeated BannerInfo list = 2;
}

message StartScriptMatchRequest {
  enum MatchMode {
    MATCH_MODE_UNSPECIFIED = 0;
    // 指定剧本
    MATCH_MODE_SPECIFY_SCRIPT = 1;
    // 快速开玩
    MATCH_MODE_QUICK_PLAY = 2;
  }

  // 匹配模式
  MatchMode mode = 1;
  // 剧本id
  uint32 script_id = 2;
}

message StartScriptMatchResponse {
  message User {
    uint32 uid = 1;
    int32 sex = 2;
    string account = 3;
    string avatar = 4;
  }

  repeated User users = 1;

  // 匹配策略 see aigc-group.proto
  uint32 strategy = 2;
  // 匹配到的群实例id
  uint32 group_id = 3;
}

message ConfirmScriptMatchRequest {
  // deprecated
  string token = 1;
  string script_match_token = 2;
}

message ConfirmScriptMatchResponse {
}

message CancelScriptMatchRequest {
}

message CancelScriptMatchResponse {
}

message TransAudioToTextReq {
  BaseRequest base_req = 1;
  // 上传obs后的key
  string audio_key = 2;
}

message TransAudioToTextResp {
  // 音频转文字
  string trans_text = 1;
  uint32 text_type = 2; // 见aigc-ext-content.proto AsrTextType
}

message GetChatNumRequest {
}

message GetChatNumResponse {
  uint32 cur_day_used_num = 1; // 今日句数使用数量
  uint32 cur_day_cfg_num = 2; // 今日句数配置数量
  uint32 available_extra_num = 3; // 额外句数有效数量
}

message StartGameRequest {
  BaseRequest base_req = 1;

  uint32 partner_id = 2;
  uint32 role_id = 3;
  string game_id = 4;
}

message StartGameResponse {
  string ctx_id = 1;
}

message GetGameInfoRequest {
  BaseRequest base_req = 1;

  string game_id = 2;
}

message GetGameInfoResponse {
  GameInfo game = 1;
}

message GetGameListRequest {
  BaseRequest base_req = 1;

  uint32 role_id = 2;
}

message GetGameListResponse {
  repeated GameInfo game_list = 1;
}

message GetOutGamesRequest {
  BaseRequest base_req = 1;

  uint32 role_id = 2;
}

message GetOutGamesResponse {
  repeated GameTopic topics = 1;
  repeated OutGames games = 2;
}

message GetTopGameListRequest {
  BaseRequest base_req = 1;

  uint32 role_id = 3;
  uint32 topic_id = 5;
}

message GetTopGameListResponse {
  repeated GameInfo games = 1;
}

message LeaveGroupRequest {
    uint32 group_id = 1;
}

message LeaveGroupResponse {
}

message GetAiAccountListRequest{
}

message  AiAccount{
  uint32 uid=1;  //用户id
  string nickname=2; //用户昵称
  string account=3; //用户头像
  uint32 sex=4;
  string signature=5;  //用户签名
  repeated string account_tags = 6;  //账号标签
  string background_image=7;
}

message GetAiAccountListResponse{
  repeated AiAccount accounts = 1;
}

message GetUserExclusiveRoleListRequest {
  string cursor = 1;
  uint32 limit = 2;
}

message GetUserExclusiveRoleListResponse {
  repeated AIRole list = 1;

  bool has_more = 2;
  string next_cursor = 3;
}
