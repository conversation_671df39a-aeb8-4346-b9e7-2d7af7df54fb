syntax = "proto3";

//import "tt/quicksilver/ga_base.proto";

option go_package = "golang.52tt.com/protocol/services/anchorcontract-go";
package anchor_contract_go;


service AnchorContractGo {
  // 获取用户合约缓存信息
  rpc GetUserContractCacheInfo(GetUserContractCacheInfoReq) returns (ContractCacheInfo){}

  // 本接口勿用，请使用BatchGetContractInfo接口
  rpc BatchGetUserContractCacheInfo(BatchGetUserContractCacheInfoReq) returns (BatchGetUserContractCacheInfoResp){}

  // 批量查询用户当前的签约信息
  rpc BatchGetContractInfo(BatchGetContractInfoReq) returns (BatchGetContractInfoResp){}


  // 查询签约用户身份变更记录
  rpc GetIdentityChangeHistory (GetIdentityChangeHistoryReq) returns (GetIdentityChangeHistoryResp){}


  rpc GetAnchorAgentUid(GetAnchorAgentUidReq) returns (GetAnchorAgentUidResp){}

  // 获取用户合约信息  此接口直接查mysql
  rpc GetUserContract(GetUserContractReq) returns (GetUserContractResp){}
  // 根据身份证号获取用户合约信息
  rpc GetContractWithIdentity(GetContractWithIdentityReq) returns (GetContractWithIdentityResp){}
  // 批量获取用户合约信息
  rpc BatchGetUserContract(BatchGetUserContractReq) returns (BatchGetUserContractResp){}
  // 批量公会成员合约信息
  rpc GetGuildContract(GetGuildContractReq) returns (GetGuildContractResp){}
  // 获取公会指定身份主播列表
  rpc GetGuildAnchorIdentity(GetGuildAnchorIdentityReq) returns (GetGuildAnchorIdentityResp){}
  // 批量获取主播身份
  rpc BatchGetAnchorIdentity(BatchGetAnchorIdentityReq) returns (BatchGetAnchorIdentityResp){}
  // 获取公会合约相关汇总信息
  rpc GetGuildContractSum(GetGuildContractSumReq) returns (GetGuildContractSumResp){}
  // 根据主播身份类型和主播类型获取签约信息
  rpc GetGuildContractByIdentity(GetGuildContractByIdentityReq) returns (GetGuildContractByIdentityResp){}

  // 获取所有合约及身份信息
  rpc GetContract(GetContractReq) returns (GetContractResp){}

  // 申请签约/主播身份
  rpc ApplySignContract(ApplySignContractReq) returns (ApplySignContractResp){}
  // 撤销签约申请
  rpc WithdrawApplySign(WithdrawApplySignReq) returns (WithdrawApplySignResp){}
  // 会长处理签约申请
  rpc PresidentHandleApplySign(PresidentHandleApplySignReq) returns (PresidentHandleApplySignResp){}
  // 会长处理所有签约申请
  rpc PresidentHandleAllApplySign(PresidentHandleAllApplySignReq) returns (PresidentHandleAllApplySignResp){}
  // 官方处理签约申请
  rpc OfficialHandleApplySign(OfficialHandleApplySignReq) returns (OfficialHandleApplySignResp){}
  // 获取用户签约申请
  rpc GetUserApplySignRecord(GetUserApplySignRecordReq) returns (GetUserApplySignRecordResp){}
  // 批量获取用户签约申请
  rpc BatchGetUserApplySignRecord(BatchGetUserApplySignRecordReq) returns (BatchGetUserApplySignRecordResp){}
  // 获取公会签约申请
  rpc GetGuildApplySignRecord(GetGuildApplySignRecordReq) returns (GetGuildApplySignRecordResp){}
  // 获取公会签约申请数量
  rpc GetGuildApplySignRecordCnt(GetGuildApplySignRecordCntReq) returns (GetGuildApplySignRecordCntResp){}
  // 获取所有签约申请
  rpc GetAllApplySignRecord(GetAllApplySignRecordReq) returns (GetAllApplySignRecordResp){}

  rpc ActorHandleExtensionContract (ActorHandleExtensionContractReq) returns (ActorHandleExtensionContractResp){}

  // 检查用户是否是纯新主播
  rpc GetIsNewbieAnchor(GetIsNewbieAnchorReq) returns (GetIsNewbieAnchorResp){}
  rpc TestSetAnchorSignDate(TestSetAnchorSignDateReq) returns (TestSetAnchorSignDateResp){}

  // 获取公会解约申请列表
  rpc GetCancelContractApplyList(GetCancelContractApplyListReq) returns (GetCancelContractApplyListResp){}
  // 申请解约
  rpc ApplyCancelContract(ApplyCancelContractReq) returns (ApplyCancelContractResp){}
  rpc ApplyCancelContractV2(ApplyCancelContractV2Req) returns (ApplyCancelContractResp){}

  // 处理解约申请
  rpc HandlerCancelContractApply(HandlerCancelContractApplyReq) returns (HandlerCancelContractApplyResp){}
  // 解约用户
  rpc CancelContractByUid(CancelContractByUidReq) returns (CancelContractByUidResp){}
  rpc CheckCanApplyCancelContract(CheckCanApplyCancelContractReq) returns (CheckCanApplyCancelContractResp){}
  // 检查是否可以解约
  rpc CheckCanApplyCancelContractV2(CheckCanApplyCancelContractV2Req) returns (CheckCanApplyCancelContractV2Resp){}

  // 解约方式解约
  // 申请解约-解约方式
  rpc ApplyCancelContractNew(ApplyCancelContractNewReq) returns (ApplyCancelContractNewResp){}
  // 从业者类型配置
  rpc GetContractWorkerConfigs(GetContractWorkerConfigsReq) returns (GetContractWorkerConfigsResp) {}
  // 获取付费解约金额
  rpc GetCancelPayAmount (GetCancelPayAmountReq) returns (GetCancelPayAmountResp) {}
  // 锁定付费解约金额
  rpc LockCancelPayAmount (LockCancelPayAmountReq) returns (LockCancelPayAmountResp) {}


  // 回收主播身份
  rpc ReclaimAnchorIdentity(ReclaimAnchorIdentityReq) returns (ReclaimAnchorIdentityResp){}
  // 回收公会下所有成员的该身份（将公会移出合作库时使用， 慎用！！）
  rpc ReclaimGuildAllAnchorIdentity(ReclaimGuildAllAnchorIdentityReq) returns (ReclaimGuildAllAnchorIdentityResp){}
  // 获取用户身份操作记录
  rpc GetUserAnchorIdentityLog(GetUserAnchorIdentityLogReq) returns (GetUserAnchorIdentityLogResp){}
  // 批量获取用户身份操作记录
  rpc BatchGetUserAnchorIdentityLog(BatchGetUserAnchorIdentityLogReq) returns (BatchGetUserAnchorIdentityLogResp){}
  // 获取公会下用户身份操作记录
  rpc GetGuildAnchorIdentityLog(GetGuildAnchorIdentityLogReq) returns (GetGuildAnchorIdentityLogResp){}

  // 获取公会下主播多人互动积分
  rpc BatchGetGuildUserScore(BatchGetGuildUserScoreReq) returns (BatchGetGuildUserScoreResp){}

  // 批量获取申请黑名单
  rpc BatchGetApplyBlacklist(BatchGetApplyBlacklistReq) returns (BatchGetApplyBlacklistResp){}
  // 获取申请黑名单
  rpc GetAllApplyBlacklist(GetAllApplyBlacklistReq) returns (GetAllApplyBlacklistResp){}
  // 操作申请黑名单
  rpc HandleApplyBlackInfo(HandleApplyBlackInfoReq) returns (HandleApplyBlackInfoResp){}

  // 获取语音主播考核
  rpc BatchGetUserLiveAnchorExamine(BatchGetUserLiveAnchorExamineReq) returns (BatchGetUserLiveAnchorExamineResp){}
  // 获取公会语音主播考核
  rpc GetGuildLiveAnchorExamine(GetGuildLiveAnchorExamineReq) returns (GetGuildLiveAnchorExamineResp){}
  // 获取语音主播考核
  rpc GetAllLiveAnchorExamine(GetAllLiveAnchorExamineReq) returns (GetAllLiveAnchorExamineResp){}
  // 更新获取语音主播考核状态
  rpc UpdateLiveAnchorExamineStatus(UpdateLiveAnchorExamineStatusReq) returns (UpdateLiveAnchorExamineStatusResp){}
  // 更新获取语音主播考核时间
  rpc UpdateLiveAnchorExamineTime(UpdateLiveAnchorExamineTimeReq) returns (UpdateLiveAnchorExamineTimeResp){}


  // 更新签约用户的经纪人信息
  rpc UpdateSignedAnchorAgentId(UpdateSignedAnchorAgentIdReq) returns (UpdateSignedAnchorAgentIdResp) {}


  //=============================考核认证标识管理============================
  // 查询考核认证标识信息
  rpc ListExamineCert(ListExamineCertReq) returns (ListExamineCertResp) {}
  // 分页查询父级考核标识
  rpc GetParentExamineCertList(CertOffsetTypeReq) returns (ParentExamineCertList) {}
  // 查看父级下面的子考核标识
  rpc GetChildExamineCertList(CertItemReq) returns (ChildExamineCertList) {}
  // 新增父级考核标识配置
  rpc AddParentExamineCert(ExamineCertInfo) returns (CertEmptyMsg) {}
  // 修改父级考核标识配置
  rpc UpdateParentExamineCert(ExamineCertInfo) returns (CertEmptyMsg) {}
  // 删除父级考核标识配置
  rpc DeleteParentExamineCert(CertItemReq) returns (CertEmptyMsg) {}
  // 新增子级考核标识配置
  rpc AddChildExamineCert(ExamineCertInfo) returns (CertEmptyMsg) {}
  // 修改子级考核标识配置
  rpc UpdateChildExamineCert(ExamineCertInfo) returns (CertEmptyMsg) {}
  // 删除子级考核标识配置
  rpc DeleteChildExamineCert(CertItemReq) returns (CertEmptyMsg) {}

  // 发放
  // 单个用户发放标识
  rpc SetUserExamineCert(UserExamineCertInfo) returns (CertEmptyMsg) {}
  // 批量用户发放标识
  rpc BatchSetUserExamineCert(BatchSetUserExamineCertReq) returns (CertEmptyMsg) {}
  // 批量检查用户发放标识
  rpc BatchCheckUserExamineCert(BatchSetUserExamineCertReq) returns (BatchSetUserExamineCertResp) {}
  // 限定主播标识发放 
  rpc SetAnchorExtraCert(SetAnchorExtraCertReq) returns (CertEmptyMsg) {}
  // 限定主播标识发放记录查询 
  rpc GetAnchorExtraCertHistory(GetAnchorExtraCertHistoryReq) returns (GetAnchorExtraCertHistoryResp) {}


  //编辑用户标识
  rpc UpdateUserExamineCert(UserExamineCertInfo) returns (CertEmptyMsg) {}

  // 回收
  // 单个用户回收标识
  rpc DelUserExamineCert(DelUserExamineCertReq) returns (CertEmptyMsg) {}
  // 批量用户回收标识
  rpc BatchDelUserExamineCert(BatchDelUserExamineCertReq) returns (CertEmptyMsg) {}
  // 查询用户标识信息
  rpc ListUserExamineCert(ListUserExamineCertReq) returns (ListUserExamineCertResp) {}

  // 获取主播考核认证标识
  rpc GetUserExamineCert(GetUserExamineCertReq) returns (GetUserExamineCertResp) {}

  // 批量获取主播考核认证标识
  rpc BatchGetUserExamineCert(BatchGetUserExamineCertReq) returns (BatchGetUserExamineCertResp) {}

  // 批量获取语音主播认证标识
  rpc BatchGetLiveAnchorCert(BatchGetLiveAnchorCertReq) returns (BatchGetLiveAnchorCertResp) {}

  // 获取用户所有认证标识(生效和未生效）
  rpc GetUserAllExamineCert(GetUserAllExamineCertReq) returns (GetUserAllExamineCertResp) {}

  // test im
  rpc SendImExamineCert(SendImExamineCertReq) returns(SendImExamineCertResp){}

  rpc GetAnchorCertTaskInfo(GetAnchorCertTaskInfoReq) returns(GetAnchorCertTaskInfoResp){}


  rpc GetAnchorCertListByItemId(GetAnchorCertListByItemIdReq) returns (GetAnchorCertListByItemIdResp) {}

  // 主播认证标签白名单配置
  rpc SetAnchorCertWhiteList(SetAnchorCertWhiteListReq)returns (CertEmptyMsg) {}

  // 导入主播内容记录
  rpc AddAnchorCertContent(AddAnchorCertContentReq) returns (CertEmptyMsg) {}
  // 查询主播内容记录
  rpc ListAnchorCertContent(ListAnchorCertContentReq) returns (ListAnchorCertContentResp) {}
  // 编辑主播内容记录
  rpc UpdateAnchorCertContent(UpdateAnchorCertContentReq) returns (CertEmptyMsg) {}

  // 新增升级任务
  rpc AddAnchorCertUpgradeTask(AddAnchorCertUpgradeTaskReq) returns (CertEmptyMsg) {}
  // 编辑升级任务
  rpc UpdateAnchorCertUpgradeTask(UpdateAnchorCertUpgradeTaskReq) returns (CertEmptyMsg) {}
  // 查询升级任务
  rpc ListAnchorCertUpgradeTask(ListAnchorCertUpgradeTaskReq) returns (ListAnchorCertUpgradeTaskResp) {}
  // 删除升级任务
  rpc DelAnchorCertUpgradeTask(DelAnchorCertUpgradeTaskReq) returns (CertEmptyMsg) {}

  //=============================考核认证标识管理 end============================


  // 获取语音主播考核状态
  rpc GetRadioLiveAnchorExamine(GetRadioLiveAnchorExamineReq) returns (GetRadioLiveAnchorExamineResp){}
  // 更新语音主播考核状态
  rpc UpdateRadioLiveAnchorExamine(UpdateRadioLiveAnchorExamineReq) returns (UpdateRadioLiveAnchorExamineResp){}

  // 获取公会星级
  rpc GetGuildLevel(GetGuildLevelReq) returns (GetGuildLevelResp) {}

  // 获取未在3天内上传音频的主播列表
  rpc GetNotUploadExamineAnchorList(GetNotUploadExamineAnchorListReq) returns (GetNotUploadExamineAnchorListResp) {}

  // 判断是否是大主播，是否有名额
  rpc CheckUserGreatLiveAnchor(CheckUserGreatLiveAnchorReq) returns (CheckUserGreatLiveAnchorResp) {}

  // 判断用户是否大主播
  rpc CheckIfGreatLiveAnchor(CheckIfGreatLiveAnchorReq) returns (CheckIfGreatLiveAnchorResp) {}


  //=============================================================
  /*会长服务后台*/
  rpc GetGuildApplySignRecordList(GetGuildApplySignRecordListReq) returns (GetGuildApplySignRecordListResp) {}
  rpc GetGuildCancelSignRecordList(GetGuildCancelSignRecordListReq) returns (GetGuildCancelSignRecordListResp) {}
  rpc GetGuildAnchorExtInfoList(GetGuildAnchorExtInfoListReq) returns (GetGuildAnchorExtInfoListResp) {}

  // 关注、取消关注
  rpc HandleFocusAnchor(HandleFocusAnchorReq) returns (HandleFocusAnchorResp) {}
  // 写备注
  rpc UpdateRemark(UpdateRemarkReq) returns (UpdateRemarkResp) {}

  // 会长发起续约
  rpc GuildExtensionContract (GuildExtensionContractReq) returns (GuildExtensionContractResp) {}
  rpc BatchGuildExtensionContract (BatchGuildExtensionContractReq) returns (BatchGuildExtensionContractResp) {}

  //end=============================================================


  rpc GetMultiPlayerCenterEntry(GetMultiPlayerCenterEntryReq) returns (GetMultiPlayerCenterEntryResp) {}

  // 获取在考核期内待上传音频的主播
  rpc GetLiveAnchorExamineNotUpload(GetLiveAnchorExamineNotUploadReq) returns (GetLiveAnchorExamineNotUploadResp) {}


  rpc ListGuildSignAnchorInfo(ListGuildSignAnchorInfoReq) returns(ListGuildSignAnchorInfoResp){}
  rpc AddGuildSignAnchorInfo(AddGuildSignAnchorInfoReq) returns(AddGuildSignAnchorInfoResp){}
  rpc GetGuildSignAnchorInfo(GetGuildSignAnchorInfoReq) returns(GetGuildSignAnchorInfoResp){}




  rpc CheckCanApplySign(CheckCanApplySignReq) returns(CheckCanApplySignResp){}

  // 申请签约公会电竞陪玩
  rpc ApplySignEsport(ApplySignEsportReq) returns(ApplySignEsportResp){}
  // 官方处理电竞陪玩签约申请
  rpc OfficialHandleApplySignEsport(OfficialHandleApplySignEsportReq) returns (OfficialHandleApplySignEsportResp){}

  rpc GetSignEsportAuditToken(GetSignEsportAuditTokenReq)returns (GetSignEsportAuditTokenResp){}

  rpc GetGuildEsportScore(GetGuildEsportScoreReq) returns (GetGuildEsportScoreResp){}

  rpc TestSignContract(TestSignContractReq) returns(TestSignContractResp){}

  // 签约平台达人
  rpc ApplySignDoyen(ApplySignDoyenReq) returns (ApplySignDoyenResp){}
  // 删除平台达人签约
  rpc DelSignDoyen(DelSignDoyenReq) returns (DelSignDoyenResp){}
  // 检查是否签约白名单
  rpc CheckIsSignWhiteUid(CheckIsSignWhiteUidReq) returns (CheckIsSignWhiteUidResp){}
  // 添加签约白名单
  rpc AddSignWhiteUid(AddSignWhiteUidReq) returns (AddSignWhiteUidResp){}
  // 删除签约白名单
  rpc DelSignWhiteUid(DelSignWhiteUidReq) returns (DelSignWhiteUidResp){}


  // 获取公会推荐置顶列表
  rpc GetRecommendTopGuildList(GetRecommendTopGuildListReq) returns (GetRecommendTopGuildListResp) {}

  //获取官方处理的解约列表
  rpc GetOfficialCancelSignList(GetOfficialCancelSignListReq) returns (GetOfficialCancelSignListResp) {}
  // 官方处理解约申请
  rpc OfficialHandleCancelSign(OfficialHandleCancelSignReq) returns (OfficialHandleCancelSignResp) {}
  // 官方备注解约申请
  rpc OfficialRemarkPayCancelSign(OfficialRemarkPayCancelSignReq) returns (OfficialRemarkPayCancelSignResp) {}
  //获取公会解约方式列表  
  rpc GetCancelContractTypeList(GetCancelContractTypeListReq) returns (GetCancelContractTypeListResp) {}
  //设置公会的解约方式
  rpc SetGuildCancelContractType(SetGuildCancelContractTypeReq) returns (SetGuildCancelContractTypeResp) {}
  //审核解约申请
  rpc CensorVideo (CensorVideoReq) returns (CensorVideoResp) {}
  //获取上传token
  rpc ContractClaimObsToken(ContractClaimObsTokenReq) returns (ContractClaimObsTokenResp){}

  // 获取权益列表
  rpc GetSignRightList(GetSignRightListReq) returns (GetSignRightListResp) {}
  //增加一级权益tab
  rpc AddSignRight(AddSignRightReq) returns (AddSignRightResp) {}
  //删除一级权益tab
  rpc DelSignRight(DelSignRightReq) returns (DelSignRightResp) {}
  //更新权益，包括新增，删除，修改子权益
  rpc UpdateSignRight(UpdateSignRightReq) returns (UpdateSignRightResp) {}
  // 获取公会签约权益列表
  rpc GetGuildSignRight(GetGuildSignRightReq) returns (GetGuildSignRightResp) {}
  //更新公会签约权益  
  rpc UpdateGuildSignRight(UpdateGuildSignRightReq) returns (UpdateGuildSignRightResp) {}

  //邀请晋升
  rpc InvitePromote(InvitePromoteReq) returns (InvitePromoteResp) {}
  // 处理晋升邀请
  rpc ProcPromoteInvite(ProcPromoteInviteReq) returns (ProcPromoteInviteResp) {}
  //获取用户的晋升邀请信息
  rpc GetUserPromoteInviteInfo(GetUserPromoteInviteInfoReq) returns(GetUserPromoteInviteInfoResp) {}

  //条件查询公会签约成员列表
  rpc GetGuildContractByCond(GetGuildContractByCondReq) returns (GetGuildContractByCondResp) {}

  //记录签约公告操作记录
  rpc RecordAnchorNoticeHandle(RecordAnchorNoticeHandleReq) returns (RecordAnchorNoticeHandleResp) {}
  //获取签约公告操作历史
  rpc GetAnchorNoticeHandleHistory(GetAnchorNoticeHandleHistoryReq) returns (GetAnchorNoticeHandleHistoryResp) {}

  // 获取是否需要进行成员身份确认
  rpc GetNeedConfirmWorkerType(GetNeedConfirmWorkerTypeReq) returns (GetNeedConfirmWorkerTypeResp) {}
  // 进行身份变更
  rpc ModifyWorkerType (ModifyWorkerTypeReq) returns (ModifyWorkerTypeResp) {}
  // 增加需要进行身份确认的用户
  rpc AddNeedConfirmWorkerType (AddNeedConfirmWorkerTypeReq) returns (AddNeedConfirmWorkerTypeResp) {}
  // 邀请成员变更身份
  rpc InviteMemberChangeWorkerType (InviteMemberChangeWorkerTypeReq) returns (InviteMemberChangeWorkerTypeResp) {}
  // 获取成员身份变更信息
  rpc GetContractChangeInfo (GetContractChangeInfoReq) returns (GetContractChangeInfoResp) {}
  // 处理成员身份变更
  rpc HandleContractChange(HandleContractChangeReq) returns (HandleContractChangeResp) {}
  // 查看拒绝理由
  rpc GetRejectReason (GetRejectReasonReq) returns (GetRejectReasonResp) {}
  // 获取协商解约原因类型
  rpc GetNegotiateReasonType (GetNegotiateReasonTypeReq) returns (GetNegotiateReasonTypeResp) {}

  rpc TestNotifyNegotiateExpire(TestNotifyNegotiateExpireReq) returns (TestNotifyNegotiateExpireResp) {}
  rpc CheckIsTotalNewMultiAnchor (CheckIsTotalNewMultiAnchorReq) returns (CheckIsTotalNewMultiAnchorResp) {}
  rpc TestHandleYearBanUser (TestHandleYearBanUserReq) returns (TestHandleYearBanUserResp) {}

  //触发定时任务
  rpc TriggerTimer(TriggerTimerReq) returns (TriggerTimerResp) {}
}



message ApplyCancelContractV2Req {
  uint32 uid = 1;
  uint32 guild_id = 2;
  bool is_check = 3;
}

message CheckCanApplySignReq {
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  uint32 identity_type = 3; // see SIGN_ANCHOR_IDENTITY
  string identity_num = 4;
}
message CheckCanApplySignResp {
}

message GetSignEsportAuditTokenReq {
  uint32 apply_id = 1;
}
message GetSignEsportAuditTokenResp {
  string audit_token = 1;
}

message ActorHandleExtensionContractReq
{
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  uint32 handle_flag = 3;       //see EXTENSION_CONTRACT_HANDLE_OPR
  uint32 contract_duration = 4;
}

message ActorHandleExtensionContractResp
{
  uint32 end_time = 1;
}


message GetIdentityChangeHistoryReq {
  SIGN_ANCHOR_IDENTITY anchor_identity = 1;
  uint32 uid = 2;
}
message GetIdentityChangeHistoryResp {
  repeated IdentityChangeInfo list = 1;
}

message IdentityChangeInfo {
  uint32 uid = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
  uint32 guild_id = 3;

  uint32 obtain_time = 4; // 获得身份的时间
  uint32 reclaim_time = 5;// 失去身份的时间，为0就是还拥有该身份
}



message GetGuildEsportScoreReq {
  uint32 guild_id = 1;
  uint32 month_ts = 2;
  uint32 page = 3;
  uint32 page_num = 4;

  uint32 query_uid = 5;
  uint32 sort_type = 6;//0-降序，1-升序
}
message GetGuildEsportScoreResp {
  uint32 total = 1;
  repeated EsporterInfo list = 2;
}
message EsporterInfo {
  uint32 anchor_uid = 1;
  uint32 income = 2;
  uint32 sign_time = 3; // 签约时间
  uint32 sign_expire_time = 4; // 签约到期时间
}


message CheckCanApplyCancelContractReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
}
message CheckCanApplyCancelContractResp {
  repeated uint32 reason_list = 1;// CANCEL_REASON_TYPE
}


message BatchGetContractInfoReq {
  repeated uint32 uids = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetContractInfoResp {
  map<uint32, ContractCacheInfo> uid2ContractInfo = 1;
}

message OfficialHandleApplySignEsportReq {
  string audit_token = 1;    // 签约申请记录id
  uint32 handle_opr = 2;  // see HANDLE_SIGN_APPLY_OPR
  string operator = 3;    // 操作者
  string remarks = 4;     // 备注
  uint32 sign_time = 6; // 签约时间
  uint32 sign_expire_time = 7; // 签约到期时间
}
message OfficialHandleApplySignEsportResp {
}

message ApplySignEsportReq {
  string audit_token = 1;
  uint32 actor_uid = 2;
  uint32 guild_id = 3;
  string identity_num = 4;
  uint32 contract_duration = 5;
}
message ApplySignEsportResp {

}


message TestSignContractReq {
  bool certain = 1;
  uint32 uid = 2;
  uint32 guild_id = 3;
  repeated uint32 anchor_identity_list = 4;
  uint32 obtain_time = 5;
}
message TestSignContractResp{
}


message GetAnchorAgentUidReq {
  repeated uint32 uids = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorAgentUidResp {
  map<uint32, uint32> uid2agentUid = 1;
}

// 合约变更类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum CONTRACT_CHANGE_TYPE {
  ENUM_CONTRACT_CHANGE_ACCEPT = 0;      // 会长同意签约
  ENUM_CONTRACT_CHANGE_DEL = 1;         // 删除合约
  ENUM_CONTRACT_CHANGE_EXTENSION = 2;    // 续约
  ENUM_CONTRACT_CHANGE_REJECT = 3;       // 会长拒绝签约
  ENUM_CONTRACT_CHANGE_TIMEOUT = 4;      // 合约过期
  ENUM_CONTRACT_CHANGE_USER_LOGOFF = 5;  // 用户注销，清理合约
  ENUM_CONTRACT_CHANGE_PROMOTE = 6;      // 晋升管理
}

// 合约操作类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum CONTRACT_OPT_TYPE {
  CONTRACT_OPT_ENUM_INVALID = 0;
  CONTRACT_OPT_ENUM_SIGN = 1;
  CONTRACT_OPT_ENUM_CANCEL = 2;
  CONTRACT_OPT_ENUM_EXTENSION = 3;
}

// 处理签约申请类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum HANDLE_SIGN_APPLY_OPR {
  HANDLE_SIGN_APPLY_OPR_INVALID = 0;
  HANDLE_SIGN_APPLY_OPR_ACCEPT = 1; // 同意
  HANDLE_SIGN_APPLY_OPR_REJECT = 2; // 拒绝
  HANDLE_SIGN_APPLY_OPR_UNHANDLE = 3;
  HANDLE_SIGN_APPLY_OPR_FAILED = 4;             // 已签约其它公会
}

// 处理续约申请类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum EXTENSION_CONTRACT_HANDLE_OPR {
  EXTENSION_CONTRACT_HANDLE_OPR_INVALID = 0;
  EXTENSION_CONTRACT_HANDLE_OPR_ACCEPT = 1;
  EXTENSION_CONTRACT_HANDLE_OPR_REJECT = 2;
  EXTENSION_CONTRACT_HANDLE_OPR_UNHANDLE = 3;
}

// 续约状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum EXTENSION_STATUS {
  EXTENSION_STATUS_CANNOT_EXTENSION = 0;      // 未达到续约条件
  EXTENSION_STATUS_CAN_EXTENSION = 1;         // 达到续约条件
  EXTENSION_STATUS_HAVE_EXTENSION = 2;        // 已发送
}

// 解约状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum CANCEL_STATUS {
  CANCEL_STATUS_FALSE = 0;                 // 不可以申请解约
  CANCEL_STATUS_TRUE = 1;                 // 可以申请解约
}

// 处理解约申请操作类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum CANCEL_APPLY_OPR {
  CANCEL_APPLY_OPR_NORMAL = 0;
  CANCEL_APPLY_OPR_REJECT = 1;
  CANCEL_APPLY_OPR_ACCEPT = 2;
}

// 签约申请状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum APPLY_SIGN_STATUS {
  APPLY_SIGN_STATUS_PRESIDENT_HANDLING = 0;   // 会长审批中
  APPLY_SIGN_STATUS_OFFICIAL_HANDLING = 1;    // 官方审批中
  APPLY_SIGN_STATUS_WITHDRAW = 2;             // 已撤销
  APPLY_SIGN_STATUS_INVALID = 3;              // 已失效
  APPLY_SIGN_STATUS_PRESIDENT_REJECT = 4;     // 会长不同意申请
  APPLY_SIGN_STATUS_OFFICIAL_REJECT = 5;      // 官方不同意申请
  APPLY_SIGN_STATUS_PASS = 6;                 // 申请通过
}

// 主播身份类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum SIGN_ANCHOR_IDENTITY {
  SIGN_ANCHOR_IDENTITY_MULTIPLAYER = 0; // 多人互动身份
  SIGN_ANCHOR_IDENTITY_RADIO_LIVE = 1;  // 语音直播身份

  SIGN_ANCHOR_IDENTITY_E_SPORTS = 2;  // 电竞陪玩
  SIGN_ANCHOR_IDENTITY_DOYEN = 3;  // 平台达人
}

// 主播身份变更操作
// buf:lint:ignore ENUM_PASCAL_CASE
enum ANCHOR_IDENTITY_CHANGE_TYPE {
  ANCHOR_IDENTITY_CHANGE_TYPE_ADD = 0;  // 新增身份
  ANCHOR_IDENTITY_CHANGE_TYPE_DEL = 1;  // 回收身份
}

// 主播类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum ANCHOR_FLAG {
  AllAnchor = 0;  // 所有类型主播
  NewSignAnchor = 1;  // 新签约主播 近30天内（含30天）签约公会并开通直播权限的主播
  SoonSignExpireAnchor = 2; // 指将于近30天内（含30天）签约到期的主播
}

// 废弃
// buf:lint:ignore ENUM_PASCAL_CASE
enum CONFORM_STATUS_TYPE{
  INCONFORM = 0;
  CONFORM = 1;
  ALL = 2;
}

// 解约原因
// buf:lint:ignore ENUM_PASCAL_CASE
enum CANCEL_REASON_TYPE {
  CANCEL_REASON_TYPE_UNKNOW = 0; // 未知原因
  CANCEL_REASON_TYPE_DAY7_NOREASON = 1; // 7天无理由
  CANCEL_REASON_TYPE_PERSON_INCOME = 2; // 个人收益不达标
  CANCEL_REASON_TYPE_GUILD_INCOME = 3; // 公会房间流水不达标
  CANCEL_REASON_TYPE_LIVE_INCOME_ACTIVE = 4;//直播收益&活跃不达标
  CANCEL_REASON_TYPE_NEGOTIATE = 5; // 协商解约
  CANCEL_REASON_TYPE_PAY = 6; // 付费解约
}


message ContractInfo {
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  uint32 sign_time = 3;
  uint32 contract_duration = 4;
  uint32 expire_time = 5;
  uint32 permission = 6;
  string identity_num = 7;
  uint32 worker_type = 8; //从业者类型ContractWorkerType
  int64 pay_amount = 9; // 核心付费解约金额
}

message AnchorIdentityInfo {
  uint32 actor_uid = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
  uint32 guild_id = 3;
  uint32 obtain_time = 4; // 身份获得时间
  uint32 agent_uid = 5;
}

message ContractCacheInfo {
  ContractInfo contract = 1;
  repeated uint32 anchor_identity_list = 2; // 拥有的主播身份列表 see SIGN_ANCHOR_IDENTITY
  repeated AnchorIdentityInfo info_list = 3;
}

message GetUserContractCacheInfoReq {
  uint32 uid = 1;
}

message GetContractReq {
  uint32 offset = 1;
  uint32 limit = 2; // 单次限制1000
}
message GetContractResp {
  uint32 total = 1;
  repeated ContractCacheInfo list = 2;
}

message GetUserContractReq {
  uint32 uid = 1;
}

message GetUserContractResp {
  ContractInfo contract = 1;
  uint32 renew = 2;                         // 续约状态 see EXTENSION_STATUS
  repeated uint32 anchor_identity_list = 3; // 拥有的主播身份列表 see SIGN_ANCHOR_IDENTITY
  uint32 contract_status = 4;               // see ga::ENUM_ContractStatus
  string status_tip = 5;
}

message GetContractWithIdentityReq {
  string identity_num = 1;
}

message GetContractWithIdentityResp {
  ContractInfo contract = 1;
}

message BatchGetUserContractReq {
  repeated uint32 uid_list = 1;
}

message BatchGetUserContractResp {
  repeated ContractInfo contract_list = 1;
}

message GetGuildContractReq {
  uint32 guild_id = 1;
  uint32 page = 2;          // 页码 从0开始
  uint32 page_size = 3;
}

message GetGuildContractResp {
  repeated ContractInfo contract_list = 1;
}

//条件查询公会签约成员列表
message GetGuildContractByCondReq {
  uint32 guild_id = 1;
  uint32 page = 2;          // 页码 从0开始
  uint32 page_size = 3;
  uint32 worker_type = 4;  // 从业者类型，is_select_worker为true时查询 see ContractWorkerType
  bool is_select_worker = 5;  // 是否筛选从业者类型
}

message GetGuildContractByCondResp {
  repeated ContractInfo contract_list = 1;
}

message GetGuildContractByIdentityReq {
  uint32 guild_id = 1;
  uint32 anchor_identity = 2; // 身份类型 see SIGN_ANCHOR_IDENTITY
  uint32 anchor_flag = 3;   // 主播类型 see ANCHOR_FLAG
  uint32 offset = 4;
  uint32 limit = 5;
  uint32 agent_uid = 6;   // 经纪人id, 为0则查询全部
  repeated uint32 agent_uid_list = 7;  // 批量经纪人查询
  repeated uint32 uid_list = 8;   //主播uid查询
}
message GetGuildContractByIdentityResp {
  repeated ContractInfo contract_list = 1;
  uint32 total_cnt = 2;
}

message GetGuildAnchorIdentityReq {
  uint32 guild_id = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
  uint32 page = 3;          // 页码 从0开始
  uint32 page_size = 4;
}

message GetGuildAnchorIdentityResp {
  repeated AnchorIdentityInfo info_list = 1;
  uint32 total = 2;
}

message BatchGetAnchorIdentityReq {
  repeated uint32 uid_list = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
}

message BatchGetAnchorIdentityResp {
  repeated AnchorIdentityInfo info_list = 1;
}

// 获取公会合约相关汇总信息
message GetGuildContractSumReq {
  uint32 guild_id = 1;
}

message GetGuildContractSumResp {
  uint32 apply_sign_count = 1;            // 向该公会申请签约的人数
  uint32 apply_cancel_count = 2;          // 向该公会申请解约的人数
  uint32 actor_count = 3;                 // 公会签约主播人数
  uint32 renewable_count = 4;             // 可发续约的数量
  uint32 expiring = 5;                    // 是否有即将到期的合约

  uint32 large_score_count = 6;            // 公会中多人互动身份成员收入积分大于15w的人数
}

// 语音主播申请时的表单信息
message LiveAnchorForm {
  uint32 tag_id = 1;      // 标签id
  string works_url = 2;   // 考核作品链接
  string contract = 3;    // 联系方式
}

message LiveAnchorExtra {
  bool great_anchor = 1;  // 是否大主播
  uint32 recharge = 2;
  repeated uint32 login_other_uids = 3;   // 疑似小号
}

message MultiAnchorExtra {
  repeated uint32 login_other_uids = 1;   // 疑似小号
  uint32 recharge = 2;                    // 近三十天累计充值
}

// 签约/申请主播身份
message ApplySignRecord {
  uint32 apply_id = 1;  // record id
  uint32 uid = 2;
  uint32 guild_id = 3;
  uint32 apply_time = 4;
  uint32 contract_duration = 5;
  string identity_num = 6;
  uint32 anchor_identity = 7;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 apply_status = 8;      // 申请状态 see APPLY_SIGN_STATUS
  LiveAnchorForm form = 9;      // 表单信息 用于语音直播身份申请审核
  LiveAnchorExtra live_anchor_extra = 10;
  MultiAnchorExtra multi_anchor_extra = 11;

  string handler = 12;
  string remarks = 13;
  uint32 change_time = 14;
  uint32 handler_time = 15; // 运营后台处理时间
}

message ApplySignContractReq {
  uint32 actor_uid = 1;
  uint32 guild_id = 2;
  string identity_num = 3;
  uint32 contract_duration = 4;
  uint32 anchor_identity = 5;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  LiveAnchorForm form = 6;      // 表单信息 用于语音直播身份申请审核
  uint32 worker_type = 7;         // 从业者类型：see ContractWorkerType
}

message ApplySignContractResp {}

message WithdrawApplySignReq {
  uint32 apply_id = 1;
}

message WithdrawApplySignResp {}

message GetUserApplySignRecordReq {
  uint32 uid = 1;
  uint32 page = 2;      // 页码 从0开始
  uint32 page_size = 3;
  repeated uint32 status_list = 4; // see APPLY_SIGN_STATUS
  bool get_all_status = 5;
}

message GetUserApplySignRecordResp {
  repeated ApplySignRecord record_list = 1;
}

message BatchGetUserApplySignRecordReq {
  repeated uint32 uid_list = 1;
  uint32 anchor_identity = 2;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  repeated uint32 status_list = 3; // see APPLY_SIGN_STATUS
  uint32 conform_status = 4; // 是否符合签约条件 1 符合，2不符合 0所有

  uint32 age = 5;
  uint32 recharge_num = 6; // 近30天累计充值金额
  uint32 begin_time = 7;
  uint32 end_time = 8;
  bool export = 9; // 是否导出
}

message BatchGetUserApplySignRecordResp {
  repeated ApplySignRecord record_list = 1;
}

message GetGuildApplySignRecordReq {
  uint32 guild_id = 1;
  uint32 anchor_identity = 2;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 page = 3;              // 页码 从0开始
  uint32 page_size = 4;
  repeated uint32 status_list = 5; // see APPLY_SIGN_STATUS
  uint32 conform_status = 6; // 是否符合签约条件 1 符合，2不符合 0所有

  uint32 age = 7;
  uint32 recharge_num = 8; // 近30天累计充值金额
  uint32 begin_time = 9;
  uint32 end_time = 10;
  bool export = 11; // 是否导出
}

message GetGuildApplySignRecordResp {
  repeated ApplySignRecord record_list = 1;
}

message GetGuildApplySignRecordCntReq {
  uint32 guild_id = 1;
  repeated uint32 status_list = 2;                // see APPLY_SIGN_STATUS
  uint32 conform_status = 3; // 是否符合签约条件 1 符合，2不符合 0所有

  uint32 age = 4;
  uint32 recharge_num = 5; // 近30天累计充值金额
  uint32 begin_time = 6;
  uint32 end_time = 7;
  bool export = 8; // 是否导出
}

message GetGuildApplySignRecordCntResp {
  map<uint32, uint32> map_apply_cnt = 1;   // anchor_identity(SIGN_ANCHOR_IDENTITY) to cnt
}

message GetAllApplySignRecordReq {
  uint32 anchor_identity = 1;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 page = 2;              // 页码 从0开始
  uint32 page_size = 3;
  repeated uint32 status_list = 4; // see APPLY_SIGN_STATUS
  uint32 conform_status = 5; // 是否符合签约条件 1 符合，2不符合 0所有

  uint32 age = 6;
  uint32 recharge_num = 7; // 近30天累计充值金额
  uint32 begin_time = 8;
  uint32 end_time = 9;
  bool export = 10; // 是否导出
}

message GetAllApplySignRecordResp {
  repeated ApplySignRecord record_list = 1;
  uint32 total = 2;
}

// 会长处理签约申请
message PresidentHandleApplySignReq {
  uint32 apply_id = 1;    // 签约申请记录id
  uint32 handle_opr = 2;  // see HANDLE_SIGN_APPLY_OPR
}

message PresidentHandleApplySignResp {
  AUDIO_LILMIT_STATUS audio_limit_status = 1;
}

message PresidentHandleAllApplySignReq {
  uint32 guild_id = 1;
  uint32 handle_opr = 2;
}

message PresidentHandleAllApplySignResp {
  uint32 handle_cnt = 1;        // 总共处理数量
  uint32 conflict_cnt = 2;      // 已经被签约的 申请数
  uint32 ok_cnt = 3;            // 成功数量
}

// 官方处理签约申请
message OfficialHandleApplySignReq {
  uint32 apply_id = 1;    // 签约申请记录id
  uint32 handle_opr = 2;  // see HANDLE_SIGN_APPLY_OPR
  string operator = 3;    // 操作者
  string remarks = 4;     // 备注
  uint32 is_white_anchor = 5;
}

message OfficialHandleApplySignResp {}

// 解约申请
message CancelContractApply {
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 apply_timestamp = 3;
  uint32 status = 4;

  repeated uint32 anchor_identity_list = 5; // 拥有的主播身份列表 see SIGN_ANCHOR_IDENTITY
  uint32 cancel_type = 6; // see CANCEL_CONTRACT_TYPE
  uint32 reason = 7; // see CANCEL_REASON_TYPE
  repeated ProofShowContent proof_list = 8; //证据列表，图片/视频
  string cancel_reason_text = 9; // 解约原因文本
  repeated string negotiate_reason_type = 10; // 协商解约类型
}

message ProofContent {
  enum ProofType{
    ProofType_Invalid = 0;
    ProofType_Video = 1; //视频
    ProofType_Image = 2; //图片
  }
  string key = 1; // 证据key
  string censor_key = 2; // 审查通过的key，用于在提交视频时确认该视频已经通过审核
  ProofType type = 3; // 证据类型
}

message GetCancelContractApplyListReq {
  uint32 guild_id = 1;
  //uint32 status = 2;
  uint32 page = 3;
  uint32 page_size = 4;
}

message GetCancelContractApplyListResp {
  repeated CancelContractApply apply_list = 1;
}

message ApplyCancelContractReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
  string guild_name = 3;
  uint32 cancel_reason = 4; // 申请解约原因 CANCEL_REASON_TYPE
}

message ApplyCancelContractResp {}

message HandlerCancelContractApplyReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 target_uid = 3;
  uint32 handle_opr = 4;    // see CANCEL_APPLY_OPR
  string reject_txt = 5;      // 会长拒绝原因
  repeated string proof_urls = 6; // 会长拒绝凭证
  repeated string proof_video_urls = 7; // 会长拒绝凭证
  uint32 apply_id = 8;
  repeated ProofContent proof_list = 9; //证据列表，图片/视频
}

message RejectApplyCancelContractResp {
}

message HandlerCancelContractApplyResp {}

message CancelContractByUidReq {
  uint32 op_uid = 1;    // op_uid为0 代表平台解约
  uint32 target_uid = 2;
  uint32 guild_id = 3;
}

message CancelContractByUidResp {}

// 回收主播身份
message ReclaimAnchorIdentityReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 anchor_identity = 3;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  string handler = 4;
}

message ReclaimAnchorIdentityResp {}

// 回收公会下所有成员的该身份（将公会移出合作库时使用， 慎用！！）
message ReclaimGuildAllAnchorIdentityReq {
  uint32 guild_id = 1;
  uint32 anchor_identity = 2;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  string handler = 3;
}

message ReclaimGuildAllAnchorIdentityResp {}

message AnchorIdentityChangeLog {
  uint32 id = 1;
  uint32 uid = 2;
  uint32 guild_id = 3;
  uint32 sign_time = 4;
  uint32 expire_time = 5;
  uint32 obtain_time = 6; // 获得身份时间
  uint32 change_time = 7;
  uint32 anchor_identity = 8;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 change_type = 9;       // see ANCHOR_IDENTITY_CHANGE_TYPE
  string handler = 10;          // 操作者
}

message GetUserAnchorIdentityLogReq {
  uint32 uid = 1;
  uint32 anchor_identity = 2;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 change_type = 3;       // see ANCHOR_IDENTITY_CHANGE_TYPE
}

message GetUserAnchorIdentityLogResp {
  repeated AnchorIdentityChangeLog list = 1;
}

message BatchGetUserAnchorIdentityLogReq {
  repeated uint32 uid_list = 1;
  uint32 anchor_identity = 2;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 change_type = 3;       // see ANCHOR_IDENTITY_CHANGE_TYPE
}

message BatchGetUserAnchorIdentityLogResp {
  repeated AnchorIdentityChangeLog list = 1;
}

message GetGuildAnchorIdentityLogReq {
  uint32 guild_id = 1;
  uint32 anchor_identity = 2;   // 所要申请的主播身份 see SIGN_ANCHOR_IDENTITY
  uint32 change_type = 3;       // see ANCHOR_IDENTITY_CHANGE_TYPE
  uint32 page = 4;
  uint32 page_size = 5;
}

message GetGuildAnchorIdentityLogResp {
  repeated AnchorIdentityChangeLog list = 1;
  uint32 total = 2;
}

message BatchGetGuildUserScoreReq {
  uint32 guild_id = 1;
  repeated uint32 uid_list = 2;
  uint32 month_time = 3;
}

message BatchGetGuildUserScoreResp {
  map<uint32, int64> map_user_score = 1;
}

message ApplyBlackInfo {
  uint32 uid = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
  uint32 begin_time = 3;
  uint32 end_time = 4;
  string handler = 5;
  string remarks = 6;
  uint32 change_time = 7;
}

message BatchGetApplyBlacklistReq {
  repeated uint32 uid_list = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
}

message BatchGetApplyBlacklistResp {
  repeated ApplyBlackInfo blacklist = 1;
}

message GetAllApplyBlacklistReq {
  uint32 identity_type = 1; // see SIGN_ANCHOR_IDENTITY
  uint32 page = 2;
  uint32 page_size = 3;
}

message GetAllApplyBlacklistResp {
  repeated ApplyBlackInfo blacklist = 1;
  uint32 total = 2;
}

message HandleApplyBlackInfoReq {
  // buf:lint:ignore ENUM_PASCAL_CASE
  enum APPLY_BLACKLIST_OPR {
    ENUM_UNKNOWN_OPR = 0;
    ENUM_REMOVE_OPR = 1;    // 移出黑名单
    ENUM_FOREVER_OPR = 2;  // 设为永久
  }
  uint32 uid = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
  uint32 handle_opr = 3; // see APPLY_BLACKLIST_OPR
  string handler = 4;
  string remarks = 5;
}

message HandleApplyBlackInfoResp {}

message LiveAnchorExamine {
  // buf:lint:ignore ENUM_PASCAL_CASE
  enum EXAMINE_STATUS {
    ENUM_HANDLING = 0;
    ENUM_PASS = 1;
    ENUM_FAILED = 2;
    ENUM_CANCEL = 3;
  }
  uint32 id = 1;
  uint32 uid = 2;
  uint32 guild_id = 3;
  uint32 status = 4;          // see EXAMINE_STATUS
  uint32 examine_time = 5;
  string giver = 6;           // 发放人
  uint32 channel_tag_id = 7;  // 直播间tag

  string handler = 8;         // 处理人
  string remarks = 9;         // 备注
  uint32 update_time = 10;
  uint32 create_time = 11;
}

message BatchGetUserLiveAnchorExamineReq {
  repeated uint32 uid_list = 1;
  repeated uint32 status_list = 2;  // see EXAMINE_STATUS
}

message BatchGetUserLiveAnchorExamineResp {
  repeated LiveAnchorExamine list = 1;
}

message GetGuildLiveAnchorExamineReq {
  uint32 guild_id = 1;
  repeated uint32 status_list = 2;  // see EXAMINE_STATUS
  uint32 page = 3;
  uint32 page_size = 4;
  uint32 from_time = 5;
  uint32 to_time = 6;
}

message GetGuildLiveAnchorExamineResp {
  repeated LiveAnchorExamine list = 1;
  uint32 total = 2;
}

message GetAllLiveAnchorExamineReq {
  repeated uint32 status_list = 1;  // see EXAMINE_STATUS
  uint32 page = 2;
  uint32 page_size = 3;
  uint32 from_time = 4;
  uint32 to_time = 5;
}

message GetAllLiveAnchorExamineResp {
  repeated LiveAnchorExamine list = 1;
  uint32 total = 2;
}

message UpdateLiveAnchorExamineStatusReq {
  uint32 id = 1;
  uint32 status = 2;          // see EXAMINE_STATUS
  string handler = 3;         // 处理人
  string remarks = 4;         // 备注
}

message UpdateLiveAnchorExamineStatusResp {}

message UpdateLiveAnchorExamineTimeReq {
  uint32 id = 1;
  uint32 examine_time = 2;
}
message UpdateLiveAnchorExamineTimeResp {}

// 更新签约用户的经纪人信息
message UpdateSignedAnchorAgentIdReq {
  uint32 guild_id = 1;
  uint32 agent_uid = 2;
  uint32 identity_type = 3; // see SIGN_ANCHOR_IDENTITY
  repeated uint32 anchor_list = 4;   // 为空时把该经纪人旗下的所有主播经纪人id置0
}
message UpdateSignedAnchorAgentIdResp {
}


//=============================考核认证标识管理============================
// buf:lint:ignore ENUM_PASCAL_CASE
enum ExamineCert_Identity_Type {
  ExamineCert_Identity_Type_MULTIPLAYER = 0; // 多人互动身份
  ExamineCert_Identity_Type_RADIO_LIVE = 1;  // 语音直播身份
}
// buf:lint:ignore ENUM_PASCAL_CASE
enum UserExamineCert_Status {
  UserExamineCert_Status_Grant = 0;   // 已发放
  UserExamineCert_Status_Effect = 1;  // 已生效
  UserExamineCert_Status_Overdue = 2; // 已过期
  UserExamineCert_Status_Recycle = 3; // 已回收
}

message CertEmptyMsg {
}

// 考核认证标识配置信息
message ExamineCertInfo {
  uint32 item_id = 1;             // 标识ID
  uint32 parent_item_id = 2;      // 父级标识ID
  uint32 identity_type = 3;       // 标识所属身份类型 see ExamineCert_Identity_Type
  string item_type = 4;           // 标识类型
  string item_name = 5;           // 标识名称
  string base_imgurl = 6;         // 标识底图
  uint32 item_level = 7;          // 标识等级
  string shadow_color = 8;        // 配置文字投影颜色
  uint32 start_time = 9;          // 上架时间
  uint32 end_time = 10;           // 下架时间
  uint32 update_time = 11;        // 更新时间
}

message ListExamineCertReq {
  oneof request_type {
    uint32 item_id = 1;     // 标识id
    string item_name = 2;   // 标识名称
    string item_type = 3;   // 标识类型
  }
  uint32 identity_type = 4;   // 标识所属身份类型
}
message ListExamineCertResp {
  repeated ExamineCertInfo list = 1;
}

message CertOffsetTypeReq {
  uint32 offset = 1;
  uint32 limit = 2;
  uint32 identity_type = 3; // 标识所属身份类型 see ExamineCert_Identity_Type
}

message CertItemReq {
  uint32 item_id = 1;
}

message ParentExamineCertList {
  uint32 total = 1;
  repeated ExamineCertInfo list = 2;
}

message ChildExamineCertList {
  repeated ExamineCertInfo list = 1;
}

// 用户考核认证标识信息
message UserExamineCertInfo {
  uint32 record_id = 1;       // 标识记录id, 用于回收
  uint32 uid = 2;
  string ttid = 3;
  string nickname = 4;        // 昵称
  uint32 item_id = 5;         // 标识ID
  string item_name = 6;       // 标识名称
  string item_type = 7;       // 标识类型
  uint32 item_level = 8;      // 标识等级
  string base_imgurl = 9;     // 标识底图
  uint32 identity_type = 10;   // 标识所属身份类型 see ExamineCert_Identity_Type
  uint32 start_time = 11;     // 生效时间
  uint32 end_time = 12;       // 过期时间
  uint32 provide_time = 13;   // 发放时间
  string handler = 14;        // 操作人
  uint32 status = 15;         // 用户标识状态 see UserExamineCert_Status
  string shadow_color = 16;        // 配置文字投影颜色
}

message BatchSetUserExamineCertReq {
  uint32 item_id = 1;         // 标识ID
  uint32 identity_type = 2;   // 标识所属身份类型
  uint32 start_time = 3;      // 生效时间
  uint32 end_time = 4;        // 过期时间
  string handler = 5;         // 操作人
  repeated uint32 uid_list = 6;
}

message BatchSetUserExamineCertResp {
  repeated uint32 conflict_uids = 1;
}

message DelUserExamineCertReq {
  uint32 record_id = 1;
  string handler = 2;         // 操作人
}

message BatchDelUserExamineCertReq {
  string handler = 1;         // 操作人
  repeated uint32 uid_list = 2;
  uint32 identity_type = 3; // 区分业务类型，语音直播、多人互动，see ExamineCert_Identity_Type
}

message ListUserExamineCertReq {
  oneof request_type {
    uint32 item_id = 1;     // 标识id
    string item_name = 2;   // 标识名称
    string item_type = 3;   // 标识类型
    string ttid = 4;
  }
  uint32 identity_type = 5;   // 标识所属身份类型
  uint32 status = 6;  // 标识状态，see UserExamineCert_Status, 传status=4为查询全部
  uint32 offset = 7;
  uint32 limit = 8;
}
message ListUserExamineCertResp {
  uint32 total = 1;
  repeated UserExamineCertInfo list = 2;
}


message ExamineCertMsg {
  uint32 uid = 1;
  uint32 item_id = 2;
  string item_name = 3;
  string base_imgurl = 4;     // 标识底图
  string shadow_color = 5;    // 配置文字投影颜色
  uint32 start_time = 6;      // 生效时间
  uint32 end_time = 7;        // 过期时间
  uint32 identity_type = 8;   // 标识所属身份类型
  uint32 level = 9; // 标识等级
}
message GetUserExamineCertReq {
  uint32 uid = 1;
}
message GetUserExamineCertResp {
  ExamineCertMsg radio_live_cert = 1; // 语音直播认证标识
  ExamineCertMsg multi_play_cert = 2; // 多人互动认证标识
  ExamineCertMsg level_cert = 3; // 主播等级标识
}

message BatchGetUserExamineCertReq {
  repeated uint32 uid_list = 1;
}
message BatchGetUserExamineCertResp {
  repeated AnchorExamineCertInfo cert_list = 1; // 只返回有认证标识的uid
}
message AnchorExamineCertInfo {
  uint32 uid = 1;
  ExamineCertMsg radio_live_cert = 2; // 语音直播认证标识
  ExamineCertMsg multi_play_cert = 3; // 多人互动认证标识
}

// 获取用户所有认证标识(生效和未生效的)
message GetUserAllExamineCertReq {
  uint32 uid = 1;
  uint32 identity_type = 2; // 区分业务类型，语音直播、多人互动，see ExamineCert_Identity_Type
}
message GetUserAllExamineCertResp {
  repeated ExamineCertMsg cert_list = 1; // 返回所有认证标识
}

message GetAnchorCertTaskInfoReq {
  uint32 uid = 1;
}
message GetAnchorCertTaskInfoResp {
  uint32 uid = 1;
  ExamineCertMsg out_cert = 2; // 外显标识
  ExamineCertMsg center_cert = 3; // 主播中心标识

  AnchorCertUpgradeTaskInfo anchor_cert_upgrade_info = 6; // 升级任务模块
}



message SendImExamineCertReq {
  uint32 uid = 1;
  uint32 item_id = 2;
  uint32 start_time = 3;
  uint32 end_time = 4;
}
message SendImExamineCertResp {}

message GetRadioLiveAnchorExamineReq {
  uint32 uid = 1;
}
message GetRadioLiveAnchorExamineResp {
  uint32 ttl = 1;
}

message UpdateRadioLiveAnchorExamineReq {
  uint32 uid = 1;
}
message UpdateRadioLiveAnchorExamineResp {
}

message GetGuildLevelReq {
  uint32 guild_id = 1;
  uint32 guild_short_id = 2;
  uint32 month_time = 3; // 时间戳
}
message GuildLevelInfo{
  uint32 guild_id = 1;
  uint32 guild_short_id = 2;
  uint32 level = 3; // 公会等级
  uint32 sign_anchor_remaine = 4; // 当日剩余的主播申报名额
  uint32 sign_anchor_limit = 5; // 每日主播申报限额
  uint32 live_anchor_cnt = 6; // 开播主播数
  uint64 total_anchor_income = 7; // 主播收礼总数
  uint64 total_channel_fee = 8; // 房间总流水
  uint32 valid_anchor_cnt = 9; // 有效主播数
  uint32 new_valid_anchor_cnt = 10; // 新增有效主播数
  uint32 potential_anchor_cnt = 11; // 潜力主播数
  uint32 new_add_anchor_cnt = 12; // 新增主播数
}
message GetGuildLevelResp {
  GuildLevelInfo info = 1;
}

message GetNotUploadExamineAnchorListReq{
  uint32 begin_ts = 1; // 起止时间，查在这段时间内的签约主播 而 3天内未上传音频 的主播信息
  uint32 end_ts = 2;
  bool white_anchor = 3; // 是否白名单
}
message ExamineAnchorInfo {
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  uint32 guild_id = 4;
}
message GetNotUploadExamineAnchorListResp{
  repeated ExamineAnchorInfo info_list = 1;
}


message CheckUserGreatLiveAnchorReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckUserGreatLiveAnchorResp {
  bool isGreat = 1; // 废弃
  uint32 sign_anchor_remaine = 2; // 废弃
  ANCHOR_APPLY_CHECK_STATUS anchor_apply_check_status = 3; // 申请签约主播账号条件状态
}


message CheckIfGreatLiveAnchorReq {
  uint32 uid = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckIfGreatLiveAnchorResp {
  bool isGreat = 1; // 是否大主播

}


message BatchGetUserContractCacheInfoReq {
  repeated uint32 uids = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetUserContractCacheInfoResp {
  map<uint32, ContractCacheInfo> uid2Contractinfo = 1;
}



//=============================================================
/*会长服务后台*/

// 公会审批状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum APPLY_SIGN_GUILD_STATUS {
  APPLY_SIGN_GUILD_STATUS_PRESIDENT_HANDLING = 0; // 公会未审批
  APPLY_SIGN_GUILD_STATUS_PRESIDENT_ACCEPT = 1; // 公会同意
  APPLY_SIGN_GUILD_STATUS_PRESIDENT_REJECT = 2; // 公会拒绝
}

// 官方审批状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum APPLY_SIGN_OFFICIAL_STATUS {
  APPLY_SIGN_OFFICIAL_STATUS_NONE = 0; // 未到官方审批流程
  APPLY_SIGN_OFFICIAL_STATUS_HANDLING = 1; // 官方待审批
  APPLY_SIGN_OFFICIAL_STATUS_ACCEPT = 2; // 官方同意
  APPLY_SIGN_OFFICIAL_STATUS_REJECT = 3; // 官方拒绝
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_APPLY_SIGN_TYPE {
  QUERY_APPLY_SIGN_TYPE_ALL = 0; // 全部申请
  QUERY_APPLY_SIGN_TYPE_HISTORY = 1; // 历史记录
}

// 签约申请信息
message ApplySignExtInfo {
  uint32 apply_id = 1;    // 签约申请记录id

  uint32 anchor_uid = 2;
  string anchor_ttid = 3;
  string anchor_account = 4;
  string anchor_nickname = 5;
  uint32 anchor_sex = 6; // 0-女，1-男

  uint32 guild_id = 7;
  uint32 apply_identity = 8; // 申请身份类型 SIGN_ANCHOR_IDENTITY
  uint32 apply_time = 9; // 申请时间
  uint32 contract_duration = 10; // 签约时长 （月）
  uint32 contract_expire_time = 11; // 合约到期时间，如果此字段非0，即已签约，为申请身份

  uint32 guild_op_status = 12; // 公会处理状态 APPLY_SIGN_GUILD_STATUS
  uint32 official_op_status = 13; // 官方处理状态 APPLY_SIGN_OFFICIAL_STATUS

  uint32 update_time = 14;
  uint32 is_newbie = 15; // 是否新手主播 0-否，1-是
}


message GetGuildApplySignRecordListReq {
  uint32          query_type = 1; // QUERY_APPLY_SIGN_TYPE
  uint32          guild_id = 2;
  repeated uint32 uid_list = 3;
  uint32          page = 4; // 第一页从0开始
  uint32          page_num = 5;
}

message GetGuildApplySignRecordListResp {
  uint32 total = 1;
  repeated ApplySignExtInfo info_list = 2;
  AUDIO_LILMIT_STATUS audio_limit_status = 3;
}


// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_CANCEL_SIGN_TYPE {
  QUERY_CANCEL_SIGN_TYPE_NOW = 0; // 解约申请
  QUERY_CANCEL_SIGN_TYPE_HISTORY = 1; // 解约记录
}



message GetGuildCancelSignRecordListReq {
  uint32          query_type = 1; // QUERY_CANCEL_SIGN_TYPE
  uint32          guild_id = 2;
  repeated uint32 uid_list = 3;
  uint32          page = 4; // 第一页从0开始
  uint32          page_num = 5;
  uint32          cancel_type = 6; // 解约类型
  uint32          start_time = 7; // 开始时间
  uint32          end_time = 8; // 结束时间
}

message GetGuildCancelSignRecordListResp {
  uint32 total = 1;
  repeated CancelSignExtInfo info_list = 2;
}

message CancelSignExtInfo {
  uint32 apply_id = 1;    // 解约申请记录id

  uint32 anchor_uid = 2;
  string anchor_ttid = 3;
  string anchor_account = 4;
  string anchor_nickname = 5;
  uint32 anchor_sex = 6; // 0-女，1-男

  uint32 guild_id = 7;
  uint32 identity = 8; //  SIGN_ANCHOR_IDENTITY

  string tag_name = 9;

  uint32 agent_uid = 10; // 经纪人uid
  string agent_ttid = 11;
  string agent_nickname = 12;

  repeated AnchorIdentityInfo identity_info_list = 13; // 身份信息
  uint32 sign_time = 14; // 签约时间
  uint32 contract_expire_time = 15; // 签约到期时间

  uint32 apply_time = 16; // 申请解约时间
  uint32 reason = 17; // 解约原因 CANCEL_REASON_TYPE
  uint32 status = 18; // 解约状态
  uint32 cancel_time = 19; // 解约生效时间
  repeated uint32 reason_list = 20;
  uint32 cancel_type = 21;  // see CANCEL_CONTRACT_TYPE
  string official_note = 22;  // 官方审批备注
  uint32 update_time = 23;  // 更新时间
  string reject_txt = 24; // 会长拒绝原因
  repeated string proof_urls = 25; // 会长拒绝凭证
  string official_notes = 26; // 官方审批结果
  string pay_desc = 27;  // 付费解约描述(申请者上传)
  int64  pay_amount = 28; // 付费解约金额(分)
  repeated string proof_video_urls = 29;  // 凭证视频列表
  uint32  worker_type = 30; // 从业者类型 see anchorcontract-go.proto的 ContractWorkerType
  repeated ProofShowContent proof_list = 31; //用户提供的证据列表，图片/视频
  string cancel_reason = 32; //用户解约原因文本
  repeated string negotiate_reason_type = 33; // 协商解约类型
}




// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_ANCHOR_TYPE {
  QUERY_ANCHOR_TYPE_ALL = 0; // 全部用户
  QUERY_ANCHOR_TYPE_MYFOCUS = 1; // 我的关注
  QUERY_ANCHOR_TYPE_TODAYLIVE = 2; // 今日开播
  QUERY_ANCHOR_TYPE_NEWSIGN = 3; // 新签约
  QUERY_ANCHOR_TYPE_CANCEL = 4; // 解约预警
  QUERY_ANCHOR_TYPE_READYEXT = 5; // 待续约
}

//晋升邀请按钮
enum PromoteInviteButton {
  PromoteInviteButton_No = 0; // 无按钮
  PromoteInviteButton_Display = 1; // 展示
  PromoteInviteButton_Gray = 2; // 置灰
}


// 签约成员信息
message AnchorExtInfo {
  uint32 anchor_uid = 1;
  string anchor_ttid = 2;
  string anchor_account = 3;
  string anchor_nickname = 4;
  uint32 anchor_sex = 5; // 0-女，1-男

  uint32 guild_id = 6;
  repeated AnchorIdentityInfo identity_info_list = 7; // 身份信息
  string tag_name = 8;

  uint32 agent_uid = 9; // 经纪人uid
  string agent_ttid = 10;
  string agent_nickname = 11;

  uint32 last_live_at = 12; // 最近开播时间
  double day30_live_hour = 13; // 30日直播时长
  uint32 day30_live_valid_day = 14; // 30日有效直播天数
  uint32 day30_hold_valid_day = 15; // 30日有效接档天数

  uint32 sign_time = 16; // 签约时间
  uint32 contract_expire_time = 17; // 签约到期时间

  bool is_focus = 18; // 会长是否关注
  bool is_extension_contract = 19; // 是否可以续约
  bool is_cancal_contract = 20; // 是否可以解约

  string remark = 21; // 备注

  uint32 invite_button = 22;  //晋升管理邀请按钮 see PromoteInviteButton
  uint32 prac_type = 23;  //从业者类型 see ContractWorkerType
  int64  pay_amount = 24; // 核心管理 付费解约金额(分)
  bool is_change_worker_type = 25; // 是否可以变更从业者类型
}

message GetGuildAnchorExtInfoListReq {
  uint32          query_type = 1; // QUERY_ANCHOR_TYPE
  uint32          guild_id = 2;
  repeated uint32 anchor_uid_list = 3;
  repeated uint32 agent_uid_list = 4;
  uint32          page = 5; // 第一页从0开始
  uint32          page_num = 6;
  bool            is_all_agent = 7;
}

message GetGuildAnchorExtInfoListResp {
  uint32 total = 1;
  repeated AnchorExtInfo info_list = 2;
}


message HandleFocusAnchorReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
  uint32 handle_opr = 3; // 1-关注，0 取消关注
}
message HandleFocusAnchorResp {}



message UpdateRemarkReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
  string remark = 3;
}
message UpdateRemarkResp {}


message GuildExtensionContractReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
}
message GuildExtensionContractResp {}

message BatchGuildExtensionContractReq{
  uint32 guild_id = 1;
  repeated uint32 anchor_uid_list = 2;
}
message BatchGuildExtensionContractResp{}


// 娱乐厅从业者中心入口
message GetMultiPlayerCenterEntryReq {
  uint32 uid = 1;
}
message GetMultiPlayerCenterEntryResp{
  string jump_url = 1; // 跳转url, 字段为空 则入口不可见
}


enum LiveAnchorExamineQueryType {
  ttid = 0;
  uid = 1;
}
message GetLiveAnchorExamineNotUploadReq {
  uint32 type = 1;//查询方式 LiveAnchorExamineQueryType
  repeated uint32 ids = 2;

  uint32 page = 3;          // 页码 从0开始
  uint32 page_size = 4;
}
message GetLiveAnchorExamineNotUploadResp {
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message info {
    uint32 uid = 1;
    string ttid = 2;
    string nickname = 3;
    string tag = 4;
    uint32 guild_id = 5;
    uint32 guild_short_id = 6; // 公会靓号
    string guild_name = 7;
    uint32 display_id = 8;
    uint32 create_time = 9;
    string channel_view_id = 10;
  }
  repeated info list = 1;
  uint32 count = 2;
}

message BatchGetLiveAnchorCertReq {
  repeated uint32 uids = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchGetLiveAnchorCertResp {
  message LiveAnchorCertInfo {
    ExamineCertMsg examine_cert = 1; // 考核认证标签
    ExamineCertMsg level_cert = 2;   // 主播等级标签
  }
  map<uint32, LiveAnchorCertInfo> uid2LiveAnchorCert = 1;
}


message SetAnchorCertWhiteListReq {
  message WhiteInfo {
    uint32 uid = 1;
    uint32 item_id = 2;
    string item_name = 3;
  }
  string handler = 1;         // 操作人
  repeated WhiteInfo list = 2;
}

enum AnchorCertContentTagType {// 0-音乐 1-情感故事 2-二次元
  AnchorCertContentTagType_Music = 0;
  AnchorCertContentTagType_EmotionStory = 1;
  AnchorCertContentTagType_TwoDimensions = 2;
}
message AnchorCertContentInfo {
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  string tag_name = 4; // 主播品类
  uint32 tag_type = 5; // 0-音乐 1-情感故事 2-二次元

  string check_level = 6;
  uint32 check_competition_status = 7; // 考核赛 1-通过
  uint32 quarter_competition_status = 8; // 季度赛
  uint32 biweekly_competition_status = 9; // 双周赛
  uint32 burn_competition_status = 10; // 燃乐考核期
  uint32 month_competition_status = 11; // 月度考核赛
  uint32 double_month_competition_status = 12; // 双月考核赛
  uint32 update_time = 13;
  string operator = 14; // 操作人
  string remark = 15; // 备注
}
message AddAnchorCertContentReq {
  uint32 tag_type = 1; // 0-音乐 1-情感故事 2-二次元
  repeated AnchorCertContentInfo list = 2;
}

message ListAnchorCertContentReq {
  uint32 uid = 1;
  uint32 query_type = 2; // 0-当前等级 1-操作记录
  uint32 tag_type = 3; // 0-音乐 1-情感故事 2-二次元
  uint32 page = 4;      // 页码 从0开始
  uint32 page_size = 5;
}
message ListAnchorCertContentResp {
  uint32 total = 1;
  repeated AnchorCertContentInfo list = 2;
}
message UpdateAnchorCertContentReq {
  AnchorCertContentInfo info = 1;
}

enum AnchorCertUpgradeTaskType {
  AnchorCertUpgradeTaskType_Invalid = 0;
  AnchorCertUpgradeTaskType_Music = 1; // 音乐类型
  AnchorCertUpgradeTaskType_EmotionStory = 2;// 情感故事类型
  AnchorCertUpgradeTaskType_TwoDimension = 3;// 二次元类型
}
enum AnchorCertUpgradeTaskStatus {
  AnchorCertUpgradeTaskStatus_Invalid = 0;
  AnchorCertUpgradeTaskStatus_Disable = 1; // 未展示
  AnchorCertUpgradeTaskStatus_Enable = 2; // 展示中
  AnchorCertUpgradeTaskStatus_Overdue = 3; // 已过期
}
message AnchorCertUpgradeTaskInfo {
  uint32 task_id = 1;
  uint32 task_type = 2; // 任务类型 AnchorCertUpgradeTaskType
  string task_name = 3; // 升级任务名称
  string task_img_url = 4; // 升级任务图片
  uint32 channel_id = 5; // cid
  string task_initial_level = 6; // 初始等级
  string task_target_level = 7; // 目标等级
  string task_check_method = 8; // 考核方式
  string task_check_time = 9; // 考核时间
  uint32 show_begin_time = 11; // 展示开始时间
  uint32 show_end_time = 12; // 展示结束时间
  string task_jump_url = 13; // 升级任务跳转链接
  uint32 status = 14; // 状态 AnchorCertUpgradeTaskStatus
}
message AddAnchorCertUpgradeTaskReq {
  AnchorCertUpgradeTaskInfo task = 1;
}

message UpdateAnchorCertUpgradeTaskReq {
  AnchorCertUpgradeTaskInfo task = 1;
}

message ListAnchorCertUpgradeTaskReq {
  uint32 task_id = 1;
  uint32 task_type = 2; // 任务类型 AnchorCertUpgradeTaskType
  string task_name = 3; // 升级任务名称
  uint32 page = 4;      // 页码 从0开始
  uint32 page_size = 5;
}
message ListAnchorCertUpgradeTaskResp {
  uint32 total = 1;
  repeated AnchorCertUpgradeTaskInfo task_list = 2;
}
message DelAnchorCertUpgradeTaskReq {
  uint32 task_id = 1;
}

message GetAnchorCertListByItemIdReq {
  repeated uint32 item_id_list = 1;
}
message GetAnchorCertListByItemIdResp {
  repeated uint32 uid_list = 1;
}

// 申请签约主播 账号条件
// buf:lint:ignore ENUM_PASCAL_CASE
enum ANCHOR_APPLY_CHECK_STATUS {
  ANCHOR_APPLY_CHECK_STATUS_ENABLE = 0; // 符合条件
  ANCHOR_APPLY_CHECK_STATUS_DISABLE = 1; // 不符合条件
}
// buf:lint:ignore ENUM_PASCAL_CASE
enum AUDIO_LILMIT_STATUS {
  AUDIO_LILMIT_STATUS_NONE = 0;
  AUDIO_LILMIT_STATUS_DAY = 1; // 本日审批达到上限
  AUDIO_LILMIT_STATUS_MONTH = 2; // 本月审批达到上限
}
message GuildSignAnchorInfo {
  uint32 guild_id = 1;
  uint32 guild_short_id = 2;
  string yearmonth = 3;
  uint32 month_before_25th_cnt = 4; // 前25日每日名额
  uint32 month_cnt = 5; // 月总名额
  uint32 month_use_cnt = 6; // 月已消耗名额
  uint32 update_time = 7;
  string operator = 8; // 操作人
}

message ListGuildSignAnchorInfoReq {
  repeated uint32 guild_short_id_list = 1;
  repeated uint32 guild_id_list = 2;
  uint32 page = 3;          // 页码 从0开始
  uint32 page_size = 4;
}
message ListGuildSignAnchorInfoResp {
  uint32 total = 1;
  repeated GuildSignAnchorInfo list = 2;
}
message AddGuildSignAnchorInfoReq {
  repeated GuildSignAnchorInfo list = 1; // 只需要传 guild_id yearmonth month_before_25th_cnt month_cnt operator
}
message AddGuildSignAnchorInfoResp {
}
message GetGuildSignAnchorInfoReq {
  uint32 guild_id = 1;
}
message GetGuildSignAnchorInfoResp {
  GuildSignAnchorInfo info = 1;
  uint32 now_day_use_cnt = 2; // 当日已消耗名额
  AUDIO_LILMIT_STATUS audio_limit_status = 3;
}


message SetAnchorExtraCertReq {
  uint32 item_id = 1;         // 标识ID
  uint32 start_time = 2;      // 生效时间
  uint32 end_time = 3;        // 过期时间
  string handler = 4;         // 操作人
  repeated uint32 uid_list = 5;
}
message GetAnchorExtraCertHistoryReq {
  uint32 begin_time = 1;
  uint32 end_time = 2;
  repeated uint32 uid_list = 3;
}
message GetAnchorExtraCertHistoryResp {
  message Info {
    uint32 item_id = 1;         // 标识ID
    uint32 start_time = 2;      // 生效时间
    uint32 end_time = 3;        // 过期时间
    string handler = 4;         // 操作人
    uint32 uid = 5;
    uint32 create_time = 6;
  }
  repeated Info list = 1;
}

//签约平台达人
message ApplySignDoyenReq {
  uint32 uid = 1;
  string identity_num = 2;
  string contract_names = 3;
}
message ApplySignDoyenResp {
}
//删除平台达人签约
message DelSignDoyenReq {
  uint32 uid = 1;
}
message DelSignDoyenResp {
}

// 判断是否签约白名单用户
message CheckIsSignWhiteUidReq {
  uint32 uid = 1;
}

message CheckIsSignWhiteUidResp {
  bool is_white = 1;
}

message AddSignWhiteUidReq {
  repeated uint32 uid = 1;
}

message AddSignWhiteUidResp {
}

message DelSignWhiteUidReq {
  repeated uint32 uid = 1;
}

message DelSignWhiteUidResp {
}

message TopGuildInfo {
  uint32 guild_id = 1; // 公会id
  uint32 rank = 2; // 排名
  string recommend_tag = 3;    // 推荐标签
  string ability_tag = 4 [deprecated = true];      // 能力标签
  string honor_title = 5;      // 荣誉称号
  int64 from_time = 6;         // 开始时间
  int64 to_time = 7;           // 结束时间
  repeated string ability_tag_list = 8; // 能力标签列表
}

// 获取推荐置顶公会列表
message GetRecommendTopGuildListReq {

}

message GetRecommendTopGuildListResp {
  repeated TopGuildInfo guild_list = 1; // 公会列表
}

//解约方式
enum CancelContractType {
  CancelContractType_Older = 0;     // 旧的解约方式
  CancelContractType_NoReason = 1;  // 无理由解约
  CancelContractType_Quiet = 2;     // 沉默解约
  CancelContractType_Negotiate = 3; // 协商解约
  CancelContractType_Pay = 4;  // 付费解约
}

//从业者类型
enum ContractWorkerType {
  ContractWorkerType_OnlineNormal = 0; // 线上普通从业者
  ContractWorkerType_Normal = 1; // 合约普通从业者
  ContractWorkerType_Manager = 2; // 核心管理者
}

//解约状态
enum CancelContractStatus {
  CancelContractStatus_Apply = 0; // 申请解约中
  CancelContractStatus_Finish = 1; // 完成解约
  CancelContractStatus_Paying = 2; // 付费解约-官方审批中
  CancelContractStatus_NegotiateReject = 3;   // 协商解约被拒绝,转到官方审批
  CancelContractStatus_NegotiateOfficeAccepted = 4; // 协商解约官方已同意
  CancelContractStatus_NegotiateOfficeReject = 5;  // 协商解约官方已拒绝
  CancelContractStatus_PayOfficeAccepted = 6; // 付费解约官方已同意
  CancelContractStatus_PayOfficeReject = 7; // 付费解约官方已拒绝
  CancelContractStatus_NegotinateFinish = 8;  // 协商解约完成(会长拒绝->官方同意->会长再同意）
  CancelContractStatus_PayFreeze = 9; // 付费解约-解约金额冻结中，只是一个临时状态，不写到数据库里
  CancelContractStatus_Abort = 100;           // 解约流程中止(特殊原因，服务端使用)
}

message ApplyCancelContractNewReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
  uint32 cancel_type = 3; // 解约类型 see CancelContractType
  repeated string proof_urls = 4; // 凭证url
  string pay_desc = 5; // 付费解约描述
  repeated ProofContent proof_list = 6; //证据列表，图片/视频
  string cancel_reason_text = 7; // 解约原因文本
  repeated string negotiate_reason_type = 8; // 协商解约类型
}
message  ApplyCancelContractNewResp {
}

//官方处理的解约信息
message OfficialCancelSignInfo {
  uint32 guild_id = 1; // 公会id
  string guild_name = 2;  //
  uint32 cancel_type = 3;  // 解约类型 see CancelContractType
  repeated string proof_urls = 4;  // 凭证列表
  uint32 apply_ts = 5;  // 申请时间
  uint32 apply_uid = 6;   // 申请人uid
  string apply_tid = 7;  // 申请人tid
  string apply_nickname = 8;  // 申请人昵称
  uint32 apply_id = 9;  // 申请id
  uint32 status = 10;   //状态：see CancelContractStatus
  string reject_txt = 11; //会长拒绝理由
  string handle_desc = 12;  // 官方处理描述
  uint32 work_type = 13; // 从业者类型 see ContractWorkerType
  int64  pay_amount = 14; // 付费解约金额(分)
  string pay_desc = 15;  // 付费解约描述(申请者上传)
  string official_remark = 16; // 官方备注
  string official_remark_oper = 17; // 官方备注操作人
  uint32 official_remark_ts = 18; // 官方备注时间
  repeated string proof_video_urls = 19;  // 凭证视频列表
  repeated ProofShowContent proof_list = 20; //用户提供的证据列表，图片/视频
  string cancel_reason = 21; //解约原因
  string negotiate_reason_type = 22; // 协商解约类型
}


message ProofShowContent {
  enum ProofType{
    ProofType_Invalid = 0;
    ProofType_Video = 1; //视频
    ProofType_Image = 2; //图片
  }
  string url = 1;
  ProofType type = 2; // 证据类型
}

enum CancelContractHandleStatus {
  CancelContractHandleStatus_Invalid = 0; // 无效
  CancelContractHandleStatus_Apply = 1; // 申请中
  CancelContractHandleStatus_Agree = 2; // 同意
  CancelContractHandleStatus_Reject = 3; // 拒绝
}

//获取官方处理的解约列表
message GetOfficialCancelSignListReq {
  enum HandleStatus {
    HandleStatus_Invalid = 0;
    HandleStatus_Running = 1; // 待处理
    HandleStatus_Done = 2;   // 已处理
  }
  uint32 cancel_type = 1;  // 解约类型 see CancelContractType
  uint32 handle_status = 2;  // 处理状态 see HandleStatus
  string tid = 3;   // tid搜索
  uint32 guild_id = 4;  // 公会id搜索
  uint32 page = 5;  // 从1开始
  uint32 page_size = 6;
  uint32 begin_time = 7;  // 开始时间
  uint32 end_time = 8;  // 结束时间
  bool is_guild = 9; // 是否公会列表
  uint32 cancel_apply_status = 10; // 解约申请状态 see CancelContractHandleStatus
}
message GetOfficialCancelSignListResp {
  repeated OfficialCancelSignInfo info_list = 1;
  uint32 total_cnt = 2;
  repeated GuildProcessingCancelSignInfo guild_info_list = 3; // 公会列表
}

message GuildProcessingCancelSignInfo {
  uint32 guild_id = 1; // 公会id
  string guild_name = 2; // 公会名称
  uint32 cancel_type = 3; // 解约类型 see CancelContractType
  string cancel_reason = 4; // 解约原因
  uint32 apply_time = 5; // 申请时间
  string apply_tid = 7;  // 申请人tid
  string apply_nickname = 8;  // 申请人昵称
  string reject_reason = 9; // 会长拒绝理由
  string negotiate_reason_type = 10; // 协商解约类型
  uint32 status = 11; // 解约状态 see CancelContractStatus
  repeated ProofShowContent proof_list = 12; //用户提供的证据列表，图片/视频
  uint32 cancel_apply_status = 13; // 解约申请状态 see CancelContractHandleStatus
}


// 官方处理解约申请
message OfficialHandleCancelSignReq {
  enum HandleResult {
    HandleResult_Invalid = 0;
    HandleResult_Agree = 1; // 同意
    HandleResult_Reject = 2;  // 拒绝
  }
  uint32 apply_id = 1;  // 申请id
  uint32 handle_result = 2;  // 处理结果 see HandleResult
  string handle_desc = 3;  // 处理描述
  string official_remark = 4;  // 官方备注
  string official_remark_oper = 5;  // 官方备注操作人
}
message OfficialHandleCancelSignResp {
}

// 官方备注解约申请
message OfficialRemarkPayCancelSignReq {
  uint32 apply_id = 1;  // 申请id
  string official_remark = 2;  // 官方备注
  string official_remark_oper = 3;  // 官方备注操作人
}
message OfficialRemarkPayCancelSignResp {
}

//解约方式信息
message CancelContractTypeInfo {
  uint32 cancel_type = 1; // 解约方式 see CancelContractType
  string tilte = 2;  //解约方式标题
  string desc = 3;   // 解约方式描述 返回html格式，h5 web页面使用的
  bool is_select = 4;   // 是否选择
  bool is_default = 5;  // 是否默认
  string desc_guild_manage = 6;  // 解约方式描述 返回html格式， 会长经营后台使用的
  uint32 version_id = 7;  // 版本号
  bool is_new = 8; // 是否新增的解约方式
}

//从业者解约方式
message PracCancelContractTypeInfo {
  uint32 prac_type = 1;  //从业者类型 see ContractWorkerType
  string time_desc = 2;  //时间描述文案
  repeated CancelContractTypeInfo info_list = 3;  //解约方式列表
  string prac_type_desc = 4;  // 从业者类型名称
  bool is_can_edit = 5;   // 是否可以编辑
  uint32 update_ts = 6;  // 更新时间
}

//获取公会解约方式列表  
message GetCancelContractTypeListReq {
  uint32 guild_id = 1;  // 公会id
}
message GetCancelContractTypeListResp {
  repeated PracCancelContractTypeInfo type_list = 1; // 解约方式列表
}

//设置公会的解约方式
message SetGuildCancelContractTypeReq {
  uint32 guild_id = 1; // 公会id
  uint32 prac_type = 2;  //从业者类型 see anchorcontract-go.proto的 ContractWorkerType
  repeated uint32 cancel_type_list = 3;  // 选中的解约方式列表
}
message SetGuildCancelContractTypeResp{
}

//子权益
message SignSubRight {
  uint32 id = 1;  // 权益id
  string name = 2;  // 名称
  string icon = 3;  // 图标
  bool is_select = 4;  // 是否选中 会长经营后台选
  uint32 sort = 5;  // 排序值
  bool is_default = 6;  // 是否默认选中(无法取消)
  string note = 7; // 备注
  bool is_new = 8; // 是否新权益
}

//签约权益
message SignRight {
  uint32 id = 1;
  string name = 2;
  repeated SignSubRight sub_right_list = 3;  // 子权益列表
  string icon = 4;  // 图标
  uint32 sort = 5;  // 排序值
  uint32 max_sub_id = 6;  // 子权益id的最大值
}


// 获取权益列表
message GetSignRightListReq {
}
message GetSignRightListResp {
  repeated SignRight right_list = 1;
}

//增加一级权益tab
message AddSignRightReq {
  string name = 1;
  string icon = 2;
  uint32 sort = 3;
}
message AddSignRightResp {
}

//删除一级权益tab
message DelSignRightReq{
  uint32 id = 1;
}
message DelSignRightResp{
}


//更新权益，包括更新一级权益名称，新增，删除，修改子权益
message UpdateSignRightReq {
  repeated SignRight right_list = 1;
}
message UpdateSignRightResp {
}


// 从业者签约权益
message PracSignRight  {
  uint32 prac_type = 1;  //从业者类型 see  ContractWorkerType
  repeated SignRight right_list = 2;  // 权益列表 
  string prac_name = 3;  // 从业者名称
}

// 获取公会签约权益列表
message GetGuildSignRightReq {
  uint32 guild_id = 1;
}
message GetGuildSignRightResp {
  repeated PracSignRight right_list = 1;
}

//更新公会签约权益  
message UpdateGuildSignRightReq {
  uint32 guild_id = 1;
  PracSignRight right = 2;
}
message UpdateGuildSignRightResp {
}


//邀请晋升
message InvitePromoteReq{
  uint32 guild_id = 1;
  uint32 invited_uid = 2;  //被邀请用户
  uint32 cancel_amount = 3;  //解约金额 单位 元
  uint32 sign_months = 4;  // 重新签约月数
}
message InvitePromoteResp {
}

// 处理晋升邀请
message ProcPromoteInviteReq {
  //处理结果
  enum ProcResType {
    ProcResType_Invalid = 0;  //无效
    ProcResType_Agree = 1;  // 同意
    ProcResType_NO_Agree = 2;  // 拒绝
  }
  uint32 proc_res = 1;  // 处理结果 see ProcResType
  uint32 id = 2; // 邀请id
  uint32 invited_uid = 3;  //被邀请uid
}
message ProcPromoteInviteResp {
}

message PromoteInfo {
  uint32 id = 1;  //邀请id
  uint32 sign_months = 2;  // 重新签约月数
  uint32 cancel_amount = 3;  //解约金额 单位 元
}

//获取用户的晋升邀请信息
message GetUserPromoteInviteInfoReq{
  uint32 uid = 1;
  uint32 guild_id = 2;  // 公会id
}
message GetUserPromoteInviteInfoResp{
  PromoteInfo info = 1;
}


// 从业者类型
message ContractWorker {
  uint32 worker_type = 1; // 从业者类型 see anchorcontract-go.proto ContractWorkerType
  string worker_name = 2; // 从业者名称
  string worker_desc = 3; // 从业者字段描述
}
message GetContractWorkerConfigsReq {
}
message GetContractWorkerConfigsResp {
  repeated ContractWorker config_list = 1;
}

// 检查是否可以解约
message CheckCanApplyCancelContractV2Req {
  uint32 uid = 1;
  uint32 guild_id = 2;
  bool   only_multi = 3;
}
message CheckCanApplyCancelContractV2Resp {
  map<uint32, string> cant_reason_map = 1; // 娱乐成员解约方式->不可解约原因
  uint32 status = 2; // 如果已有解约申请，解约的阶段
  uint32 status_end_time = 3; // 协商解约阶段结束时间戳
  map<uint32, string> show_but_disable_map = 4; // 显示但不可选的方式->不可选的原因
  uint32 cancel_type = 5; //  如果已有解约申请，解约的方式
  string reject_reason = 6; // 如果已有解约申请，拒绝理由
  repeated ProofShowContent proof_list = 7; // 如果已有解约申请，用户提供的证据列表，图片/视频
}

message GetCancelPayAmountReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
  bool   is_upgrade = 3; // 是否是晋级
}
message GetCancelPayAmountResp {
  int64 amount = 1; // 解约金额
  int64 lock_amount = 2; // 锁定金额
  int64 lock_expire_ts = 3; // 锁定金额过期时间
  int64 lock_start_ts = 4;  //开始时间戳
}

message LockCancelPayAmountReq {
  uint32 uid = 1;
  uint32 guild_id = 2;
}
message LockCancelPayAmountResp {
  int64 lock_amount = 1; // 锁定金额
  int64 lock_expire_ts = 2; // 锁定金额过期时间
  int64 lock_start_ts = 3;  //开始时间戳
}

// 审核上传的视频
message CensorVideoReq {
  uint32 guild_id = 1;
  uint32 uid = 2;
  repeated string video_key = 3; // 视频key
}

message CensorVideoResp {
  repeated CensorResult censor_result = 1;
}

message CensorResult {
  string video_key = 1;
  string censor_key = 2; // 审查通过的key，用于在提交申请时确认该视频已经通过审核
  string transcode_url = 3; // 转码后的url，预览用
}

message ContractClaimObsTokenReq {
  uint32 expiration = 1; // optional, seconds,
}

message ContractClaimObsTokenResp{
  string token = 1;
  int64 expire_at = 2; // token 过期时间戳
}

// 字段： 公会id、会长id、公告内容、申请审核时间、操作人、操作时间、操作、备注
message AnchorNoticeHandle {
  uint32 guild_id = 1; // 公会id
  uint32 uid = 2; // 会长id
  string notice_content = 3; // 公告内容
  uint32 apply_time = 4; // 申请审核时间
  string operator = 5; // 操作人
  uint32 update_time = 6; // 操作时间
  uint32 operate_type = 7; // 操作类型
  string remark = 8; // 备注
}


message RecordAnchorNoticeHandleReq {
  AnchorNoticeHandle notice = 1; // 处理记录
}

message RecordAnchorNoticeHandleResp {
}

message GetAnchorNoticeHandleHistoryReq {
  uint32 guild_id = 1; // 公会id
  uint32 uid = 2; // 会长id
  uint32 page = 3; // 页码 从0开始
  uint32 page_size = 4;
}

message GetAnchorNoticeHandleHistoryResp {
  uint32 total = 1; // 总记录数
  repeated AnchorNoticeHandle notice_list = 2; // 处理记录列表
}

// 获取是否需要进行成员身份确认
message GetNeedConfirmWorkerTypeReq {
  uint32 uid = 1; // 用户id
  uint32 guild_id = 2; // 公会id
}

message GetNeedConfirmWorkerTypeResp {
  bool need_confirm = 1; // 是否需要身份确认
}

// 进行成员身份修改
message ModifyWorkerTypeReq {
  uint32 worker_type = 1; // 从业者类型 see anchorcontract-go.proto ContractWorkerType
  uint32 uid = 2;
}

message ModifyWorkerTypeResp {
}

// 增加需要进行身份确认的用户
message AddNeedConfirmWorkerTypeReq{
  uint32 uid = 1; // 用户id
  uint32 guild_id = 2; // 公会id
}

message  AddNeedConfirmWorkerTypeResp{
}


message InviteMemberChangeWorkerTypeReq {
  uint32 guild_id = 1; // 公会ID
  uint32 uid = 2; // 用户ID
  uint32 worker_type = 3; // 从业者类型 see anchorcontract-go.proto的 ContractWorkerType
}

message InviteMemberChangeWorkerTypeResp {
}

message GetContractChangeInfoReq {
  uint32 id = 1; // 唯一id，用于查看对应的权益变更
}

message GetContractChangeInfoResp {
  repeated ContractPrivilegeGroupChange group_list = 1; // 权益组列表
  repeated CanCancelContractChange cancel_types = 2; // 解约方式
  bool is_accept = 3; // 是否已经接受
}

message CanCancelContractChange{
  repeated CancelContractTypeInfo cancel_types = 1; // 现有的解约方式
  repeated CancelContractTypeInfo removed_cancel_types = 2; // 减少的解约方式
}

message ContractPrivilegeGroupChange {
  string name = 1; // 权益组名称
  repeated SignSubRight privilege_list = 2; // 权益列表
  repeated SignSubRight removed_privilege_list = 3; // 减少的权益列表
}

message HandleContractChangeReq {
  uint32 id = 1; // 唯一id
  bool is_agree = 2; // 是否同意
  uint32 uid = 3; // 用户id
}
message HandleContractChangeResp {
}

// 查看拒绝理由
message GetRejectReasonReq {
  uint32 id = 1; // 唯一id
  uint32 uid = 2; // 用户id
}
message GetRejectReasonResp {
  string reject_reason = 1; // 拒绝理由
  repeated ProofShowContent proof_list = 2; //证据列表，图片/视频
}

// 获取协商解约类型
message GetNegotiateReasonTypeReq {
}

message GetNegotiateReasonTypeResp {
  repeated string negotiate_reason_type = 1; // 可选的协商解约类型
}

message TestNotifyNegotiateExpireReq{

}

message TestNotifyNegotiateExpireResp{
}

message CheckIsTotalNewMultiAnchorReq {
  uint32 uid = 1; // 用户id
}

message CheckIsTotalNewMultiAnchorResp {
  bool is_total_new = 1; // 是否是全新多人主播
}

message TestHandleYearBanUserReq{
}

message TestHandleYearBanUserResp{
}


//触发定时任务
message TriggerTimerReq {
   // 定时任务类型
   enum TimerType
   {
      Timer_Type_Invalid = 0;  // 无效
      Timer_Type_SettleAnchorCertEmotionStoryTimer = 1;  // 认证等级-情感故事
      Timer_Type_SettleAnchorCertMusicTimer = 2;  // 认证等级-音乐
      Timer_Type_SettleAnchorCertTwoDimensionsTimer = 3;  // 认证等级-二次元
   }

   TimerType timer_type = 1; // 定时任务类型
}
message TriggerTimerResp {
}


message GetIsNewbieAnchorReq {
  uint32 uid = 1; // 用户id
}
message IdentityNewbieInfo {
  uint32 identity_type = 1; // 身份id
  uint32 obtain_day = 2; // 获得身份的天数
  bool is_newbie = 3; // 是否是新手身份
  uint32 obtain_ts = 4; // 获得身份的时间戳
}
message GetIsNewbieAnchorResp {
  repeated IdentityNewbieInfo identity_list = 2; // 新手主播身份列表
  bool is_on = 3; // 是否功能开启
  uint32 sign_day = 4; // 签约天数
  uint32 guild_id = 5; // 公会id
 }


 message TestSetAnchorSignDateReq {
  uint32 uid = 1; // 用户id
  uint32 guild_id = 2; // 公会id
  uint32 sign_ts = 3; // 签约时间戳
  map<uint32, uint32> identity_obtain_ts = 4; // 身份类型->获得时间戳
 }
message TestSetAnchorSignDateResp {
}