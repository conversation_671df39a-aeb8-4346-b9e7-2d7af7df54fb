syntax = "proto3";

package channel_play_tab;
option go_package = "golang.52tt.com/protocol/services/channel-play-tab";


service ChannelPlayTab {
  // 批量获取玩法可见用户白名单
  rpc BatchGetWhiteUidListByTabIds(BatchGetWhiteUidListByTabIdsReq) returns (BatchGetWhiteUidListByTabIdsResp) {}
  // 保存玩法白名单,仅运营后台用
  rpc SetWhiteUidListByTabId(SetWhiteUidListByTabIdReq) returns (SetWhiteUidListByTabIdResp) {}
  // 获取指定类型的tab
  rpc GetTabsByTabSubType(GetTabsByTabSubTypeReq) returns (GetTabsByTabSubTypeResp) {}
  // 插入/更新tab,仅运营后台用
  rpc UpsertTab(UpsertTabReq) returns (UpsertTabResp) {}
  // 删除tab,仅运营后台用
  rpc DeleteTab(DeleteTabReq) returns (DeleteTabResp) {}
  // 获取所有tab,读缓存
  rpc GetTabs(GetTabsReq) returns (GetTabsResp) {}
  // 插入/更新玩法或者分类的未成年监管控制开关
  rpc UpsertMinorSupervisionConfig(UpsertMinorSupervisionConfigReq) returns (UpsertMinorSupervisionConfigResp) {}
  // 批量获取玩法或者分类的未成年监管控制开关
  rpc BatchGetMinorSupervisionConfig(BatchGetMinorSupervisionConfigReq) returns (BatchGetMinorSupervisionConfigResp) {}

  // （未成年）玩法开关协议配置 运营后台
  rpc GetTabsRealNameConfigs(GetTabsRealNameConfigsReq)returns(GetTabsRealNameConfigsResp);
  rpc UpdateTabsRealNameConfig(UpdateTabsRealNameConfigReq)returns(UpdateTabsRealNameConfigResp);
  rpc AddTabsRealNameConfig(AddTabsRealNameConfigReq)returns(AddTabsRealNameConfigResp);
  rpc DeleteTabsRealNameConfig(DeleteTabsRealNameConfigReq)returns(DeleteTabsRealNameConfigResp);
  // 用户入口筛选tab开关展示
  rpc GetUserTabsRealNameConfig(GetUserTabsRealNameConfigReq)returns(GetUserTabsRealNameConfigResp);

  // 快速匹配相关新接口 旧的首页玩法卡GetHomePagePlayCards相关和GetDetailTabInfoOfScene，GetSceneTabsForTT都不用了，后面统一使用新的
  rpc GetQuickMatchConfig(GetQuickMatchConfigReq) returns (GetQuickMatchConfigResp);
  rpc UpdateQuickMatchConfig(UpdateQuickMatchConfigReq) returns (UpdateQuickMatchConfigResp);
  rpc DeleteQuickMatchConfig(DeleteQuickMatchConfigReq) returns (DeleteQuickMatchConfigResp);
  rpc ResortQuickMatchConfig(ResortQuickMatchConfigReq) returns (ResortQuickMatchConfigResp);

  // 新版首页快速匹配入口配置 运营后台
  rpc UpsertNewQuickMatchConfig(UpsertNewQuickMatchConfigReq) returns (UpsertNewQuickMatchConfigResp);
  rpc BatchGetNewQuickMatchConfig(BatchGetNewQuickMatchConfigReq) returns (BatchGetNewQuickMatchConfigResp);
  rpc DelNewQuickMatchConfig(DelNewQuickMatchConfigReq) returns (DelNewQuickMatchConfigResp);
  // 客户端获取配置
  rpc GetNewQuickMatchConfig(GetNewQuickMatchConfigReq) returns (GetNewQuickMatchConfigResp);

  // 极速版PC分类运营后台接口
  rpc GetFastPCCategoryConfig(GetFastPCCategoryConfigReq) returns (GetFastPCCategoryConfigResp);
  rpc UpsertFastPCCategoryConfig(UpsertFastPCCategoryConfigReq) returns (UpsertFastPCCategoryConfigResp);
  rpc DelFastPCCategoryConfig(DelFastPCCategoryConfigReq) returns (DelFastPCCategoryConfigResp);
  rpc SortFastPCCategoryConfig(SortFastPCCategoryConfigReq) returns (SortFastPCCategoryConfigResp);

  // 全局玩法数据扩展
  rpc GetAllTabInfoExt (GetAllTabInfoExtReq) returns (GetAllTabInfoExtResp);
  rpc UpsertTabInfoExt (UpsertTabInfoExtReq) returns (UpsertTabInfoExtResp);
  rpc DelTabInfoExt (DelTabInfoExtReq) returns (DelTabInfoExtResp);

  // 用户禁止配置 运营后台
  rpc UpsertBanUserConfig(UpsertBanUserConfigReq) returns (UpsertBanUserConfigResp) {}
  rpc GetBanUserConfigList(GetBanUserConfigListReq) returns (GetBanUserConfigListResp) {}
  rpc DelBanUserConfig(DelBanUserConfigReq) returns (DelBanUserConfigResp) {}
  rpc BatchAddBanUserConfig(BatchAddBanUserConfigReq) returns (BatchAddBanUserConfigResp) {}
  // 客户端获取配置
  rpc GetActiveBanUserConfigWithCache(GetActiveBanUserConfigWithCacheReq) returns (GetActiveBanUserConfigWithCacheResp) {}
  rpc GetBanGameHallUser(GetBanGameHallUserReq) returns (GetBanGameHallUserResp) {}
  rpc UpdateBanGameHallGetStatus(UpdateBanGameHallGetStatusReq) returns (UpdateBanGameHallGetStatusResp) {}
}

//批量获取玩法可见用户白名单
message BatchGetWhiteUidListByTabIdsReq{
  repeated uint32 tab_ids = 1;// 玩法id列表
  string source = 2;//请求来源，用于区分请求的来源,如“运营后台-全局配置”、“调用方服务名”
  bool no_cache = 3; //false使用缓存，true不使用缓存
}

message BatchGetWhiteUidListByTabIdsResp{
  message WhiteUidList {
    repeated uint32 list = 1;//白名单用户uid
  }
  //map中没有对应玩法key是没有配置白名单,只返回有白名单列表不为空的玩法白名单列表
  map<uint32, WhiteUidList> tab_list_map = 1; //key 为玩法id，value为WhiteUidList
}

//保存玩法白名单,仅运营后台用
message SetWhiteUidListByTabIdReq{
  uint32 tab_id = 1; //玩法id
  repeated uint32 uid_list = 2; //uid白名单
  string source = 3;//请求来源，用于区分请求的来源,如“运营后台-全局配置”、“调用方服务名”
}

message SetWhiteUidListByTabIdResp{

}

enum TabSubType {
  GAME = 0; //游戏分类
  COMPREHENSIVE_CHANNEL = 1;  //综合频道分类
}

message Tab {
  uint32 tab_id = 1;  // 玩法id
  string tab_name = 2;  // 玩法名称
  string image_url = 3; // 外显图片
  TabSubType tab_sub_type = 4; // 玩法子分类
  bool is_tab_visible = 5;  // 控制tab是否可见，用于【综合频道】tab的展示控制
}

message UpsertTabReq {
  Tab tab = 1;
}

message UpsertTabResp {
}

message GetTabsByTabSubTypeReq {
  TabSubType tab_sub_type = 1; // 获取指定类型的tab
  string source = 2;  // 请求来源，用于区分请求的来源,如“运营后台-全局配置”、“调用方服务名”
  bool no_cache = 3;  // false使用缓存，true不使用缓存
}

message GetTabsByTabSubTypeResp {
  repeated Tab tabs = 1;
}

message DeleteTabReq {
  uint32 tab_id = 1;
}

message DeleteTabResp {
}

message GetTabsReq {
  bool no_cache = 1;  // false使用缓存，true不使用缓存
  bool only_visible = 2;  // false查所有tab，true查可见的tab
}

message GetTabsResp {
  repeated Tab tabs = 1;
}

enum MinorSupervisionScene {
  None = 0;
  EnterRoom = 1;  // 进房
  JoinGame = 2;   // 加入游戏
}

enum MinorSupervisionConfigType {
  ConfigTypeNone = 0;
  ConfigTypeTab = 1;  // 玩法的配置
  ConfigTypeCategory = 2;   // 分类的配置
}

message MinorSupervisionConfig {
  string id = 1;
  uint32 tab_id = 2;      // 玩法id
  uint32 category_id = 3; // 分类id
  repeated uint32 market_ids = 4; // 需要未成年认证的马甲包
  repeated MinorSupervisionScene switch_on_scenes = 5; // 开关状态数组,值为打开状态的场景
  int64 need_check_register_time = 6;   // 单位:ms,这个时间之后注册的用户需要开启未成年监管,为空则不生效
  MinorSupervisionConfigType config_type = 7; // 配置类型,区分玩法配置还是分类配置
}

message UpsertMinorSupervisionConfigReq {
  MinorSupervisionConfig config = 1;
}

message UpsertMinorSupervisionConfigResp {
}

message BatchGetMinorSupervisionConfigReq {
  repeated uint32 tab_ids = 1;  // 玩法id列表
  repeated uint32 category_ids = 2; // 分类id列表
  string source = 3;  // 请求来源，用于区分请求的来源,如“运营后台-全局配置”、“调用方服务名”
  bool no_cache = 4;  // false使用缓存，true不使用缓存
  bool return_all = 5; // 是否返回全量数据
}

message BatchGetMinorSupervisionConfigResp {
  map<uint32, MinorSupervisionConfig> tab_configs = 1;  // key: tab_id
  map<uint32, MinorSupervisionConfig> category_configs = 2; // key: category_id
}

message GetTabsRealNameConfigsReq {
  uint32 page = 1;  // 页数
  uint32 count = 2; // 页面大小
}

message TabsRealNameConfig {
  uint32 market_id = 1;
  string channel_pkg = 2; // 渠道包
  string cli_ver = 3; // 客户端版本号，"0.0.0"表示全版本
  bool switch_status = 4; // 开关状态
  repeated uint32 tab_ids = 5; // 具体的玩法列表
  repeated uint32 category_ids = 6; // 具体的类目列表
}

message GetTabsRealNameConfigsResp {
  repeated TabsRealNameConfig configs = 1;
  uint32 total = 2; // 配置记录总数
}

message UpdateTabsRealNameConfigReq {
  TabsRealNameConfig config = 1;
}

message UpdateTabsRealNameConfigResp {
}

message AddTabsRealNameConfigReq {
  TabsRealNameConfig config = 1;
}

message AddTabsRealNameConfigResp {
}

message DeleteTabsRealNameConfigReq {
  uint32 market_id = 1;
  string channel_pkg = 2; // 渠道包
  string cli_ver = 3; // 客户端版本号
}

message DeleteTabsRealNameConfigResp {
}

message GetUserTabsRealNameConfigReq {
  uint32 market_id = 1;
  string channel_pkg = 2; // 用户登录渠道包
  uint32 cli_ver = 3; // 客户端版本号
}

message GetUserTabsRealNameConfigResp {
  TabsRealNameConfig configs = 1;
}

// 快速匹配相关的
enum QuickMatchConfigType {
  Invalid = 0;
  NormalQuickMatchList = 1; //快速匹配半屏兜底列表
  MoreCardTabList = 2; //快速匹配更多默认列表
  NewQuickMatch = 3;//新版快速匹配，使用GetNewQuickMatchConfig
  MiniZoneExposeQuickMatchList = 4; //休闲互动专区快速匹配外显列表
  MiniZoneMoreTabList = 5; //休闲互动专区快速匹配更多列表
  MiniZoneHotAreaGameList = 6; //休闲互动专区重点模块区域
}

message SceneInfo {
  string id = 1;
  uint32 tab_id = 2;
  uint32 tab_type = 3; //见tab.proto
  uint32 category_id = 4;
  QuickMatchConfigType config_type = 5;    // 1-快速匹配半屏兜底列表；2-快速匹配更多默认列表

  //休闲互动专区快速匹配配置字段
  string game_display_name = 6; //快速匹配游戏外显名称
  repeated string bg_colors = 7;  //背景色值
  uint32 button_effect = 8;  //按钮效果，快速匹配or短链,1快速匹配，2短链，见channel-play_.proto ButtonEffect
  string jump_link = 9;  //跳转短链，当button_effect=2时有效

  // 休闲互动专区重点区域配置额外字段
  HotMiniGameExtraInfo mini_game_extra_info = 10;  // 重点区域游戏配置额外信息，config_type=6时有效
}

message HotMiniGameExtraInfo {
  // 介绍文案
  string intro_text = 1;
  // 背景图 url
  string bg_url = 2;
  // 背景颜色
  string bg_color = 3;
  // 按钮文案
  string button_text = 4;
  // 按钮颜色组
  repeated string button_colors = 5;
  // 动图 lottie url
  string lottie_url = 6;
}

message GetQuickMatchConfigReq {
  QuickMatchConfigType config_type = 1;
  uint32 page = 2;
  uint32 limit = 3;
}

message GetQuickMatchConfigResp {
  repeated SceneInfo scene_info = 1;
}

message UpdateQuickMatchConfigReq {
  SceneInfo scene_info = 1;
}

message UpdateQuickMatchConfigResp {

}

message DeleteQuickMatchConfigReq {
  string id = 1;
}

message DeleteQuickMatchConfigResp {

}

message ResortQuickMatchConfigReq {
  repeated string ids = 1;
}

message ResortQuickMatchConfigResp {

}

message NewQuickMatchConfig {
  string id = 1;
  uint32 tab_id = 2;  // 玩法id, 每个玩法对应一个配置
  string tab_name = 3;
  string title = 4; // 标题
  string button_text = 5; // 按钮文案
  uint32 position = 6; // 展示位置, 快速匹配入口展示在列表第position个位置
  int64 updated_at = 7; // 更新时间, 单位:ms
}

message UpsertNewQuickMatchConfigReq {
  NewQuickMatchConfig config = 1;
}

message UpsertNewQuickMatchConfigResp {
}

message BatchGetNewQuickMatchConfigReq {
  uint32 tab_id = 1;  // deprecated
  repeated uint32 tab_ids = 2;  // 玩法id列表,不传拿所有
}

message BatchGetNewQuickMatchConfigResp {
  repeated NewQuickMatchConfig configs = 1;
}

message DelNewQuickMatchConfigReq {
  string id = 1;
}

message DelNewQuickMatchConfigResp {
}

message GetNewQuickMatchConfigReq {
  uint32 tab_id = 1;
}

message GetNewQuickMatchConfigResp {
  NewQuickMatchConfig config = 1;
}

message GetFastPCCategoryConfigReq {

}

enum FastPCCategoryType {
  FAST_PC_CATEGORY_TYPE_NORMAL = 0;
  FAST_PC_CATEGORY_TYPE_MINI_GAME = 1; // 小游戏分类
  FAST_PC_CATEGORY_TYPE_MELEE = 2; // 团战分类
}

message FastPCCategoryConfig {
  uint32 id = 1;
  string name = 2;
  // 分类下玩法排序按数组位序
  repeated uint32 tab_ids = 3;
  // 分类排序
  uint32 sort = 4;
  int64 updated_at = 5;
  // 分类图标
  string icon = 6;
  // 分类类型
  FastPCCategoryType category_type = 7;
}

message GetFastPCCategoryConfigResp {
  repeated FastPCCategoryConfig configs = 1;
}

message UpsertFastPCCategoryConfigReq {
  FastPCCategoryConfig config = 1;
}

message UpsertFastPCCategoryConfigResp {
}

message DelFastPCCategoryConfigReq {
  uint32 id = 1;
}

message DelFastPCCategoryConfigResp {
}

message SortFastPCCategoryConfigReq {
  repeated uint32 ids = 1;
}

message SortFastPCCategoryConfigResp {
}

// 配置玩法信息扩展内容，不同业务使用不同的结构体映射处理，往后新增
enum TabInfoExtEnum {
  TAB_INFO_EXT_NO_TYPE = 0; // 默认无
  TAB_INFO_EXT_FAST_PC_CONFIG = 1; // 极速PC配置，see TabInfoExtFastPcConfig
}

message GetAllTabInfoExtReq {
  TabInfoExtEnum ext_type = 1;
}

message TabInfoExtItem {
  uint32 tab_id = 1;
  TabInfoExtEnum ext_type = 2;
  int64 update_ts = 4;    // 更新时间，单位:ms
  oneof ext_content {
    TabInfoExtFastPcConfig fast_pc_config = 6;
  }
}

message TabInfoExtFastPcConfig {
  // 新增PC极速版背景图
  string fast_pc_home_page_background_img_url = 1;
  // 新增PC极速版大厅房间背景图
  string fast_pc_room_background_img_url = 2;
}

message GetAllTabInfoExtResp {
  repeated TabInfoExtItem items = 1;
}

message UpsertTabInfoExtReq {
  TabInfoExtItem item = 1;
}

message UpsertTabInfoExtResp {

}

message DelTabInfoExtReq {
  uint32 tab_id = 1;
  TabInfoExtEnum ext_type = 2;
}

message DelTabInfoExtResp {

}

// 禁止配置对象类型
enum BanConfigType {
  BanConfigTypeNone = 0;
  BanConfigTypeUser = 1; // 单个用户禁止配置
  BanConfigTypeCrowdGroup = 2; // 人群包禁止配置
  BanConfigTypeMultiUser = 3; // 多个用户禁止配置
}

// 配置生效状态
enum Status {
  StatusNone = 0;
  StatusActive = 1; // 生效中
  StatusInEffective = 2; // 未生效
  StatusExpired = 3; // 已过期
}

// 禁止配置业务类型
enum BanPostType {
  BanPostTypePost = 0;  // 禁止专区发帖配置
  BanPostTypeGamePalCard = 1; // 限制使用游戏搭子功能配置
  BanPostTypeHideGamePalCard = 2; // 屏蔽用户发布的搭子卡配置
  BanGameHallSay = 3; // 限制组队大厅发言
}

message MultiUserInfo {
  string ttid = 1;  // 用户ttid
  uint32 uid = 2;  // 用户uid
  string user_nickname = 3; // 用户昵称
}

message BanUserPostConfig {
  string id = 1;
  BanConfigType config_type = 2;  // 禁止配置对象类型
  string ttid = 3;                // 用户ttid
  string crowd_group_id = 4;      // 人群包id
  repeated uint32 tab_ids = 5;    // 玩法id列表
  int64 start_time = 6;   // 禁止发帖开始时间
  int64 end_time = 7;     // 禁止发帖结束时间
  string ban_reason = 8;  // 禁止原因
  int64 operate_time = 9; // 操作时间
  string operator = 10;   // 操作者
  Status status = 11;     // 配置生效状态
  string crowd_group_name = 12; // 人群包名称
  uint32 crowd_group_user_count = 13; // 人群包人数
  string user_nickname = 14;  // 用户昵称
  BanPostType ban_post_type = 15; // 禁止配置业务类型
  uint32 uid = 16; // 用户uid
  string warning_message = 17; // 助手推送警告文案
  repeated string ttids = 18;  // 用户ttids
  repeated uint32 uids = 19;   // 用户uids
  map<string, MultiUserInfo> user_info_map = 20; // 用户信息
}

// 批量添加禁止用户使用搭子卡功能/屏蔽搭子卡配置
message BatchAddBanUserConfigReq {
  repeated string ttids = 1;// 用户ttid
  int64 start_time = 2;     // 禁止使用开始时间
  int64 end_time = 3;       // 禁止使用结束时间
  string ban_reason = 4;    // 禁止原因
  string warning_message = 5; // 助手推送警告文案
  repeated BanPostType ban_post_types = 6; // 业务类型，每种类型单独创建一条配置
  string operator = 7;    // 操作者
}

message BatchAddBanUserConfigResp {
}

// 创建/更新用户禁止配置
message UpsertBanUserConfigReq {
  BanUserPostConfig config = 1;
}

message UpsertBanUserConfigResp {
}

// 获取禁止配置列表
message GetBanUserConfigListReq {
  uint32 page = 1;     // 页数
  uint32 size = 2;     // 页面大小
  bool need_count = 3; // 是否需要返回total
  string ttid = 4;     // 根据ttid筛选
  Status status = 5;   // 根据配置生效状态筛选
  repeated BanPostType ban_post_types = 6;  // 根据类型筛选配置
}

message GetBanUserConfigListResp {
  repeated BanUserPostConfig configs = 1;
  uint32 total = 2;
}

// 删除禁止配置
message DelBanUserConfigReq {
  string id = 1;
}

message DelBanUserConfigResp {
}

message ActiveBanUserConfig {
  BanConfigType config_type = 1;  // 禁止配置类型
  uint32 uid = 2;                 // 用户uid
  string crowd_group_id = 3;      // 人群包id
  uint32 tab_id = 4;              // 禁止的玩法id
  string ban_reason = 5;          // 禁止原因
}

// 获取生效中的禁止用户配置
message GetActiveBanUserConfigWithCacheReq {
  uint32 tab_id = 1;  // 玩法id
  uint32 uid = 2;     // 用户uid
  BanPostType ban_post_type = 3;  // 禁止配置业务类型
}

message GetActiveBanUserConfigWithCacheResp {
  repeated ActiveBanUserConfig configs = 1;
}

message GetBanGameHallUserReq {
}

message GetBanGameHallUserResp {
  repeated BanUserPostConfig configs = 1;
}

message UpdateBanGameHallGetStatusReq {
  repeated string ids = 1;
}

message UpdateBanGameHallGetStatusResp {
}

message Button {
  string text = 1; // 按钮文案
  string link = 2; // 按钮链接
  string color = 3; // 按钮颜色
}

enum PushToType {
  PUSH_TO_TYPE_UNSPECIFIED = 0; // 未指定
  PUSH_TO_TYPE_ALL = 1; // 全部用户
  PUSH_TO_TYPE_CROWD = 2; // 人群包

}
message DesktopPushCfg {
  string id = 1; // 配置id
  string background_img = 2; // 背景图
  string title = 3; // 标题
  repeated Button buttons = 4; // 按钮列表

}