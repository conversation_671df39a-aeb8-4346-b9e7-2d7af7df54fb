syntax = "proto3";

package chat_bot;

option go_package = "golang.52tt.com/protocol/services/chat-bot";

import "tt/quicksilver/chat-bot/chat-bot.proto";
import "tt/quicksilver/aigc/aigc-soulmate-middle/aigc-soulmate-middle.proto";

enum EventAction {
  EVENT_ACTION_INVALID = 0;
  EVENT_ACTION_DELETE_PARTNER = 1; // 删除单个机器人事件
  EVENT_ACTION_DELETE_ROLE = 2; // 删除角色
  EVENT_ACTION_DELETE_CATEGORY = 3; // 删除分类
  EVENT_ACTION_SWITCH_ROLE_DELETE_PARTNER = 4; // 切换角色删除机器人
  EVENT_ACTION_BATCH_DELETE_ROLE = 5; // 运营后台批量删除角色

}

message AIPartnerStatusChangeEvent {
  EventAction action = 1; // 事件类型
  repeated uint32 partner_id = 2; // 被删除的机器人id，EVENT_ACTION_DELETE_PARTNER 有效
  uint32 role_id = 3; // 被删除的角色id, EVENT_ACTION_DELETE_ROLE 有效
  uint32 uid = 4; // 删除者id
  string category_id = 5; //被删除的分类id, EVENT_ACTION_DELETE_CATEGORY
  repeated uint32 role_id_list = 6; // 运营后台批量删除的角色， EVENT_ACTION_BATCH_DELETE_ROLE 有效
}

message CreateAIPartnerEvent {
  uint32 uid = 1;
  uint32 role_id = 2;
  // AI伴侣来源 see chat-bot.proto enum AIPartner.Source
  uint32 source = 3;
}

message UpdateAIRoleEvent {
  message OfficialUpdateInfo{
    uint32 role_id = 1;
    // 强插位置
    uint32 insert_pos = 2;
    // 是否展示/曝光到首页 true:展示 false:不展示
    bool exposed = 3;
    // 展示分类id
    string category_id = 4;
  }
  repeated OfficialUpdateInfo role_info_list = 1;
}

// 角色点赞事件
message AIRoleLikeEvent {
  enum Action {
    ACTION_INVALID = 0;
    ACTION_LIKE = 1; // 点赞
    ACTION_UNLIKE = 2; // 取消点赞
  }

  uint32 uid = 1;
  uint32 role_id = 2;

  Action action = 3;
  int64 triggered_at = 4;
}

// 角色创建/更新事件
message AIRoleChangeEvent {
  enum Op {
    OP_INVALID = 0;
    OP_CREATE = 1; // 创建
    OP_UPDATE = 2; // 更新
    OP_REPORT = 3; // 举报
  }

  // 角色进入触发文案
  message RoleNthText {
    // 第n次
    uint32 seq = 1;
    // 第n次进入触发文案
    string text = 2;
  }

  // 群聊角色属性配置
  message GroupRoleConfig {
    // 群聊开场白
    repeated AIGroupPrologue prologues = 1;
    // 群聊角色描述
    string chat_character = 2;
    // 关系描述
    string relation_character = 3;
    // 角色描述
    string desc = 4;
  }

  // 多人剧本玩法角色属性配置
  message PlayRoleConfig {

  }

  // 群开场白
  message AIGroupPrologue {
    // 开场白文本内容
    string text = 1;
    // 开场白语音链接
    string audio = 2;
    // 开场白顺序
    uint32 priority = 3;
  }

  message RoleInfo {
    string name = 1;
    uint32 source = 2; // 创建来源
    uint32 uid = 3; //  用户自己创建时（create_type=1）时，必填，
    uint32 type = 4; // 角色类型
    uint32 state = 5; // 状态
    uint32 sex = 6; // 0:女 1:男 2:其他
    string background_image = 7; // 背景图url
    string avatar = 8; // 头像url
    string character = 9; // 角色说明，可为空
    string prompt_id = 10; // 泼墨体ID，
    string prompt_version = 11; // 泼墨体版本ID
    bool enable_rcmd_reply = 12; // 是否开启推荐回复
    string prologue = 13; // 开场白
    string prologue_voice = 14; // 开场白语音文件
    string category_id = 15; // 角色分类
    repeated string tags = 16; // 角色标签
    string timbre = 17; // 音色, 格式待定，先暂定为string
    repeated RoleNthText nth_text_list = 18;// 第n次进入触发文案
    uint32 audit_result = 19; // 审核结果
    uint32 insert_pos = 20; // 强插位置
    bool exposed = 21; // 是否首页曝光
    string story_id = 22; // 故事ID
    GroupRoleConfig group_role_config = 23; // 群聊角色属性配置
    string setting_info = 24; // 用户角色设置
  }

  Op op = 1;

  uint32 id = 2;
  RoleInfo role = 3;
  uint32 reporter_uid = 4;
}

message AIMsgEvent {
    message Peer {
        enum Type {
            TYPE_UNSPECIFIED = 0;

            // 用户
            TYPE_USER = 1;

            // 树洞伴侣
            TYPE_TREEHOLE_PARTNER = 2;
            // 多角色伴侣
            TYPE_MULTIROLE_PARTNER = 3;
            // 桌宠伴侣
            TYPE_PET_PARTNER = 4;
        }

        // uid, partner_id
        uint32 id = 1;
        Type type = 2;

        // USER: username
        // PARTNER: {uid}:{role_id}
        string account = 3;
    }

    // 消息发送者
    Peer sender = 1;
    // 消息接收者
    Peer receiver = 2;

    // 消息
    chat_bot.ImMsg msg = 3;
    // 回复的消息ID
    string reply_msg_id = 4;

    // 消息分段索引
    uint32 seg_idx = 5;
    // 消息分段数
    uint32 seg_cnt = 6;

    chat_bot.ImTriggerMsgType trigger_msg_type = 7;
}

message AIGroupMsgEvent {
    message Sender {
        enum Type {
            TYPE_UNSPECIFIED = 0;
            // 用户
            TYPE_USER = 1;
            // 角色
            TYPE_ROLE = 2;
        }

        // uid, group_id
        uint32 id = 1;
        Type type = 2;
    }

    // 发送者
    Sender sender = 1;

    // 消息
    aigc_soulmate_middle.GroupTimeLineMsg msg = 3;

    // 群类型 see aigc-group.proto GroupType
    uint32 group_type = 4;

    // 句数是否已用完
    bool is_use_up = 5;
}

message SendingMsg {
    message CtxInfo {
        string ctx_id = 1;
        string ctx_scene = 2;

        string game_id = 3;
    }

    chat_bot.ImMsg msg = 1;

    // 玩法上报需要
    string game_type = 2;

    uint32 msg_type = 3;
    uint32 reply_type = 4;
    uint32 scene_type = 5;
    uint32 content_type = 6;
    // see chat-bot.proto enum ImTriggerMsgType
    uint32 trig_type = 8;
    // see aigc-soulmate-middle.proto enum SentenceType
    uint32 sentence_type = 9;

    uint32 seg_idx = 16;
    uint32 seg_cnt = 17;

    // 关联的上下文信息
    CtxInfo ctx_info = 21;
}

// 单聊消息发送事件
message SingleMsgSendEvent {
    uint32 uid = 2;
    uint32 partner_id = 3;

    SendingMsg sending_msg = 4;
    chat_bot.SendOption opt = 5;
}
